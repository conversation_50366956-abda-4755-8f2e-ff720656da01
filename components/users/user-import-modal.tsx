"use client";

import { useState, useRef } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Separator } from "@/components/ui/separator";
import {
  Upload,
  FileText,
  CheckCircle,
  AlertTriangle,
  X,
  Download,
  Users,
  Loader2,
} from "lucide-react";
import { useAppStore } from "@/stores/app-store";
import { useRTL } from "@/contexts/rtl-context";
import { cn } from "@/lib/utils";
import { parseCSVImport, type ImportResult } from "@/lib/data-export-import";
import { toast } from "sonner";

interface UserImportModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function UserImportModal({ open, onOpenChange }: UserImportModalProps) {
  const { importUsers } = useAppStore();
  const { isRTL, direction } = useRTL();
  const fileInputRef = useRef<HTMLInputElement>(null);

  const [isProcessing, setIsProcessing] = useState(false);
  const [importResult, setImportResult] = useState<ImportResult | null>(null);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [dragOver, setDragOver] = useState(false);

  const handleFileSelect = (file: File) => {
    if (file.type !== "text/csv" && !file.name.endsWith(".csv")) {
      toast.error("يرجى اختيار ملف CSV فقط");
      return;
    }

    setSelectedFile(file);
    setImportResult(null);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(false);

    const files = Array.from(e.dataTransfer.files);
    if (files.length > 0) {
      handleFileSelect(files[0]);
    }
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(false);
  };

  const processFile = async () => {
    if (!selectedFile) return;

    setIsProcessing(true);
    try {
      const text = await selectedFile.text();
      const result = parseCSVImport(text);
      setImportResult(result);

      if (result.success && result.data) {
        toast.success(`تم تحليل ${result.validRows} مستخدم بنجاح`);
      } else {
        toast.error("فشل في تحليل الملف");
      }
    } catch (error) {
      toast.error("خطأ في قراءة الملف");
      setImportResult({
        success: false,
        errors: ["خطأ في قراءة الملف"],
      });
    } finally {
      setIsProcessing(false);
    }
  };

  const confirmImport = () => {
    if (importResult?.success && importResult.data) {
      importUsers(importResult.data);
      toast.success(`تم استيراد ${importResult.data.length} مستخدم بنجاح`);
      onOpenChange(false);
      resetModal();
    }
  };

  const resetModal = () => {
    setSelectedFile(null);
    setImportResult(null);
    setIsProcessing(false);
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  };

  const downloadTemplate = () => {
    const template = [
      "معرف المستخدم,الاسم,الهاتف,النوع,السنة,الكلية,القسم,تاريخ الميلاد,العنوان,الفيسبوك,تاريخ أول حضور",
      "USR001,أحمد محمد,01234567890,ذكر,1,كلية العلوم,قسم الرياضيات,1995-01-15,القاهرة,https://facebook.com/ahmed,2024-01-01",
      "USR002,فاطمة علي,01987654321,أنثى,2,كلية التربية,قسم اللغة العربية,1994-05-20,الجيزة,,2024-01-01",
    ].join("\n");

    const BOM = "\uFEFF";
    const blob = new Blob([BOM + template], {
      type: "text/csv;charset=utf-8;",
    });

    const link = document.createElement("a");
    link.href = URL.createObjectURL(blob);
    link.download = "template_users.csv";
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent
        className="max-w-4xl max-h-[90vh] overflow-y-auto"
        dir={direction}
      >
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2 text-xl">
            <Upload className="h-5 w-5" />
            استيراد المستخدمين
          </DialogTitle>
          <DialogDescription>
            قم برفع ملف CSV يحتوي على بيانات المستخدمين لاستيرادها إلى النظام
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Template Download */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-lg flex items-center gap-2">
                <Download className="h-4 w-4" />
                تحميل النموذج
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-gray-600 mb-3">
                قم بتحميل نموذج ملف CSV لمعرفة التنسيق المطلوب
              </p>
              <Button variant="outline" onClick={downloadTemplate}>
                <Download className="h-4 w-4 mr-2" />
                تحميل نموذج CSV
              </Button>
            </CardContent>
          </Card>

          {/* File Upload */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-lg flex items-center gap-2">
                <FileText className="h-4 w-4" />
                رفع الملف
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div
                className={cn(
                  "border-2 border-dashed rounded-lg p-8 text-center transition-colors",
                  dragOver ? "border-blue-500 bg-blue-50" : "border-gray-300",
                  selectedFile ? "border-green-500 bg-green-50" : ""
                )}
                onDrop={handleDrop}
                onDragOver={handleDragOver}
                onDragLeave={handleDragLeave}
              >
                {selectedFile ? (
                  <div className="space-y-2">
                    <CheckCircle className="h-12 w-12 text-green-600 mx-auto" />
                    <p className="font-medium">{selectedFile.name}</p>
                    <p className="text-sm text-gray-600">
                      {(selectedFile.size / 1024).toFixed(1)} KB
                    </p>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        setSelectedFile(null);
                        setImportResult(null);
                      }}
                    >
                      <X className="h-4 w-4 mr-2" />
                      إزالة الملف
                    </Button>
                  </div>
                ) : (
                  <div className="space-y-4">
                    <Upload className="h-12 w-12 text-gray-400 mx-auto" />
                    <div>
                      <p className="text-lg font-medium">
                        اسحب وأفلت ملف CSV هنا
                      </p>
                      <p className="text-sm text-gray-600">
                        أو انقر لاختيار ملف
                      </p>
                    </div>
                    <Button
                      variant="outline"
                      onClick={() => fileInputRef.current?.click()}
                    >
                      <FileText className="h-4 w-4 mr-2" />
                      اختيار ملف
                    </Button>
                  </div>
                )}
              </div>

              <input
                ref={fileInputRef}
                type="file"
                accept=".csv"
                onChange={(e) => {
                  const file = e.target.files?.[0];
                  if (file) handleFileSelect(file);
                }}
                className="hidden"
              />

              {selectedFile && !importResult && (
                <div className="mt-4 text-center">
                  <Button
                    onClick={processFile}
                    disabled={isProcessing}
                    className="btn-gradient"
                  >
                    {isProcessing ? (
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    ) : (
                      <FileText className="h-4 w-4 mr-2" />
                    )}
                    {isProcessing ? "جاري التحليل..." : "تحليل الملف"}
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Import Results */}
          {importResult && (
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-lg flex items-center gap-2">
                  {importResult.success ? (
                    <CheckCircle className="h-4 w-4 text-green-600" />
                  ) : (
                    <AlertTriangle className="h-4 w-4 text-red-600" />
                  )}
                  نتائج التحليل
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* Statistics */}
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-blue-600">
                      {importResult.totalRows || 0}
                    </div>
                    <div className="text-sm text-gray-600">إجمالي الصفوف</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-green-600">
                      {importResult.validRows || 0}
                    </div>
                    <div className="text-sm text-gray-600">صفوف صحيحة</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-red-600">
                      {importResult.errors?.length || 0}
                    </div>
                    <div className="text-sm text-gray-600">أخطاء</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-yellow-600">
                      {importResult.warnings?.length || 0}
                    </div>
                    <div className="text-sm text-gray-600">تحذيرات</div>
                  </div>
                </div>

                {/* Progress Bar */}
                {importResult.totalRows && importResult.validRows && (
                  <div>
                    <div className="flex justify-between text-sm mb-2">
                      <span>معدل النجاح</span>
                      <span>
                        {Math.round(
                          (importResult.validRows / importResult.totalRows) *
                            100
                        )}
                        %
                      </span>
                    </div>
                    <Progress
                      value={
                        (importResult.validRows / importResult.totalRows) * 100
                      }
                      className="h-2"
                    />
                  </div>
                )}

                {/* Errors */}
                {importResult.errors && importResult.errors.length > 0 && (
                  <Alert>
                    <AlertTriangle className="h-4 w-4" />
                    <AlertDescription>
                      <div className="font-medium mb-2">الأخطاء المكتشفة:</div>
                      <ul className="list-disc list-inside space-y-1 text-sm">
                        {importResult.errors.slice(0, 5).map((error, index) => (
                          <li key={index}>{error}</li>
                        ))}
                        {importResult.errors.length > 5 && (
                          <li>
                            ... و {importResult.errors.length - 5} أخطاء أخرى
                          </li>
                        )}
                      </ul>
                    </AlertDescription>
                  </Alert>
                )}

                {/* Warnings */}
                {importResult.warnings && importResult.warnings.length > 0 && (
                  <Alert>
                    <AlertTriangle className="h-4 w-4" />
                    <AlertDescription>
                      <div className="font-medium mb-2">التحذيرات:</div>
                      <ul className="list-disc list-inside space-y-1 text-sm">
                        {importResult.warnings.map((warning, index) => (
                          <li key={index}>{warning}</li>
                        ))}
                      </ul>
                    </AlertDescription>
                  </Alert>
                )}
              </CardContent>
            </Card>
          )}
        </div>

        <DialogFooter className="gap-2">
          <Button
            variant="outline"
            onClick={() => {
              onOpenChange(false);
              resetModal();
            }}
          >
            <X className="h-4 w-4 mr-2" />
            إلغاء
          </Button>

          {importResult?.success && importResult.data && (
            <Button onClick={confirmImport} className="btn-gradient">
              <Users className="h-4 w-4 mr-2" />
              استيراد {importResult.data.length} مستخدم
            </Button>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
