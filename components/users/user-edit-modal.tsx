"use client";

import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import {
  User,
  Phone,
  Calendar,
  MapPin,
  GraduationCap,
  BookOpen,
  Facebook,
  Camera,
  Save,
  X,
  Upload,
  Loader2,
} from "lucide-react";
import { useAppStore, type User as AppUser } from "@/stores/app-store";
import { useRTL } from "@/contexts/rtl-context";
import { cn } from "@/lib/utils";
import { format } from "date-fns";
import { toast } from "sonner";

const formSchema = z.object({
  user_id: z.string().min(3, "معرف المستخدم مطلوب"),
  name: z.string().min(2, "الاسم يجب أن يكون أكثر من حرفين"),
  phone: z.string().min(11, "رقم الهاتف يجب أن يكون 11 رقم على الأقل"),
  gender: z.enum(["male", "female"], { required_error: "يرجى اختيار النوع" }),
  year: z.enum(["1", "2", "3", "4"], {
    required_error: "يرجى اختيار السنة الدراسية",
  }),
  college_category: z.enum(["science", "education", "other"], {
    required_error: "يرجى اختيار فئة الكلية",
  }),
  custom_college: z.string().optional(),
  department: z.string().min(2, "اسم القسم مطلوب"),
  birthdate: z.string().min(1, "تاريخ الميلاد مطلوب"),
  address: z.string().min(5, "العنوان يجب أن يكون أكثر تفصيلاً"),
  facebook_url: z.string().optional(),
  first_attendance_date: z.string().min(1, "تاريخ أول حضور مطلوب"),
});

type FormData = z.infer<typeof formSchema>;

interface UserEditModalProps {
  user: AppUser | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function UserEditModal({
  user,
  open,
  onOpenChange,
}: UserEditModalProps) {
  const { updateUser } = useAppStore();
  const { isRTL, direction } = useRTL();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [photoFile, setPhotoFile] = useState<File | null>(null);
  const [photoPreview, setPhotoPreview] = useState<string | null>(null);

  const form = useForm<FormData>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      user_id: "",
      name: "",
      phone: "",
      gender: undefined,
      year: undefined,
      college_category: undefined,
      custom_college: "",
      department: "",
      birthdate: "",
      address: "",
      facebook_url: "",
      first_attendance_date: "",
    },
  });

  // Reset form when user changes
  useEffect(() => {
    if (user && open) {
      // Determine college category
      let collegeCategory: "science" | "education" | "other" = "other";
      let customCollege = user.college;

      if (user.college.includes("علوم")) {
        collegeCategory = "science";
        customCollege = "";
      } else if (user.college.includes("تربية")) {
        collegeCategory = "education";
        customCollege = "";
      }

      form.reset({
        user_id: user.user_id,
        name: user.name,
        phone: user.phone,
        gender: user.gender,
        year: user.year.toString() as "1" | "2" | "3" | "4",
        college_category: collegeCategory,
        custom_college: customCollege,
        department: user.department,
        birthdate: user.birthdate,
        address: user.address,
        facebook_url: user.facebook_url || "",
        first_attendance_date: user.first_attendance_date,
      });
      setPhotoPreview(null);
      setPhotoFile(null);
    }
  }, [user, open, form]);

  const handlePhotoChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setPhotoFile(file);
      const reader = new FileReader();
      reader.onload = (e) => {
        setPhotoPreview(e.target?.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  const onSubmit = async (data: FormData) => {
    if (!user) return;

    setIsSubmitting(true);
    try {
      // Determine final college name
      let finalCollege = "";
      if (data.college_category === "science") {
        finalCollege = "كلية العلوم";
      } else if (data.college_category === "education") {
        finalCollege = "كلية التربية";
      } else {
        finalCollege = data.custom_college || "كلية أخرى";
      }

      const updatedUserData = {
        user_id: data.user_id,
        name: data.name,
        phone: data.phone,
        gender: data.gender,
        year: Number.parseInt(data.year) as 1 | 2 | 3 | 4,
        college: finalCollege,
        department: data.department,
        birthdate: data.birthdate,
        address: data.address,
        facebook_url: data.facebook_url,
        first_attendance_date: data.first_attendance_date,
      };

      // Update user in store
      updateUser(user.id, updatedUserData);

      toast.success("تم تحديث بيانات المستخدم بنجاح");
      onOpenChange(false);
    } catch (error) {
      toast.error("حدث خطأ أثناء تحديث البيانات");
    } finally {
      setIsSubmitting(false);
    }
  };

  const onError = (errors: any) => {
    console.error("Form validation errors:", errors);
    toast.error("يرجى التحقق من البيانات المدخلة");
  };

  if (!user) return null;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent
        className="max-w-4xl max-h-[90vh] overflow-y-auto"
        dir={direction}
      >
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2 text-xl">
            <User className="h-5 w-5" />
            تعديل بيانات المستخدم - {user.name}
          </DialogTitle>
          <DialogDescription>
            قم بتعديل البيانات الشخصية والأكاديمية للمستخدم
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(onSubmit, onError)}
            className="space-y-6"
          >
            {/* Photo Upload Section */}
            <div className="flex items-center gap-4 p-4 bg-gray-50 rounded-lg">
              <Avatar className="h-20 w-20">
                <AvatarImage src={photoPreview || undefined} />
                <AvatarFallback className="text-lg font-bold bg-gradient-to-r from-blue-500 to-purple-500 text-white">
                  {user.name.charAt(0)}
                </AvatarFallback>
              </Avatar>
              <div className="flex-1">
                <h3 className="font-medium mb-2">الصورة الشخصية</h3>
                <div className="flex items-center gap-2">
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() =>
                      document.getElementById("photo-upload")?.click()
                    }
                  >
                    <Camera className="h-4 w-4 mr-2" />
                    تغيير الصورة
                  </Button>
                  <input
                    id="photo-upload"
                    type="file"
                    accept="image/*"
                    onChange={handlePhotoChange}
                    className="hidden"
                  />
                  {photoFile && (
                    <Badge variant="secondary" className="text-xs">
                      تم اختيار صورة جديدة
                    </Badge>
                  )}
                </div>
              </div>
            </div>

            {/* Personal Information */}
            <div className="space-y-4">
              <div className="flex items-center gap-2 mb-4">
                <User className="h-5 w-5 text-blue-600" />
                <h3 className="text-lg font-semibold">البيانات الشخصية</h3>
              </div>

              <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
                <FormField
                  control={form.control}
                  name="user_id"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="flex items-center gap-2">
                        <User className="h-4 w-4 text-purple-600" />
                        معرف المستخدم
                      </FormLabel>
                      <FormControl>
                        <Input
                          placeholder="USR001"
                          className="font-mono"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="flex items-center gap-2">
                        <User className="h-4 w-4 text-blue-600" />
                        الاسم الكامل
                      </FormLabel>
                      <FormControl>
                        <Input placeholder="أدخل الاسم الكامل" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="phone"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="flex items-center gap-2">
                        <Phone className="h-4 w-4 text-green-600" />
                        رقم الهاتف
                      </FormLabel>
                      <FormControl>
                        <Input placeholder="01xxxxxxxxx" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="gender"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>النوع</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        value={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="اختر النوع" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="male">ذكر</SelectItem>
                          <SelectItem value="female">أنثى</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="birthdate"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="flex items-center gap-2">
                        <Calendar className="h-4 w-4 text-purple-600" />
                        تاريخ الميلاد
                      </FormLabel>
                      <FormControl>
                        <Input type="date" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>

            <Separator />

            {/* Academic Information */}
            <div className="space-y-4">
              <div className="flex items-center gap-2 mb-4">
                <GraduationCap className="h-5 w-5 text-green-600" />
                <h3 className="text-lg font-semibold">البيانات الأكاديمية</h3>
              </div>

              <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="year"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>السنة الدراسية</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        value={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="اختر السنة" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="1">السنة الأولى</SelectItem>
                          <SelectItem value="2">السنة الثانية</SelectItem>
                          <SelectItem value="3">السنة الثالثة</SelectItem>
                          <SelectItem value="4">السنة الرابعة</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="college_category"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>فئة الكلية</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        value={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="اختر فئة الكلية" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="science">كلية العلوم</SelectItem>
                          <SelectItem value="education">
                            كلية التربية
                          </SelectItem>
                          <SelectItem value="other">كلية أخرى</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              {/* Custom College Field */}
              {form.watch("college_category") === "other" && (
                <FormField
                  control={form.control}
                  name="custom_college"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="flex items-center gap-2">
                        <GraduationCap className="h-4 w-4 text-orange-600" />
                        اسم الكلية
                      </FormLabel>
                      <FormControl>
                        <Input placeholder="أدخل اسم الكلية" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              )}

              <FormField
                control={form.control}
                name="department"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="flex items-center gap-2">
                      <BookOpen className="h-4 w-4 text-indigo-600" />
                      القسم/التخصص
                    </FormLabel>
                    <FormControl>
                      <Input
                        placeholder="أدخل اسم القسم أو التخصص"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="first_attendance_date"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="flex items-center gap-2">
                      <Calendar className="h-4 w-4 text-green-600" />
                      تاريخ أول حضور
                    </FormLabel>
                    <FormControl>
                      <Input type="date" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <Separator />

            {/* Contact Information */}
            <div className="space-y-4">
              <div className="flex items-center gap-2 mb-4">
                <MapPin className="h-5 w-5 text-red-600" />
                <h3 className="text-lg font-semibold">معلومات الاتصال</h3>
              </div>

              <FormField
                control={form.control}
                name="address"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="flex items-center gap-2">
                      <MapPin className="h-4 w-4 text-red-600" />
                      العنوان
                    </FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="أدخل العنوان التفصيلي"
                        className="min-h-[80px]"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="facebook_url"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="flex items-center gap-2">
                      <Facebook className="h-4 w-4 text-blue-600" />
                      رابط الفيسبوك (اختياري)
                    </FormLabel>
                    <FormControl>
                      <Input
                        placeholder="https://facebook.com/username"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </form>
        </Form>

        <DialogFooter className="gap-2">
          <Button
            type="button"
            variant="outline"
            onClick={() => onOpenChange(false)}
            disabled={isSubmitting}
          >
            <X className="h-4 w-4 mr-2" />
            إلغاء
          </Button>
          <Button
            type="submit"
            onClick={form.handleSubmit(onSubmit, onError)}
            disabled={isSubmitting}
            className="btn-gradient"
          >
            {isSubmitting ? (
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
            ) : (
              <Save className="h-4 w-4 mr-2" />
            )}
            {isSubmitting ? "جاري الحفظ..." : "حفظ التغييرات"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
