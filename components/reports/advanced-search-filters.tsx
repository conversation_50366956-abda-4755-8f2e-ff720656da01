"use client";

import React, { useState, useCallback } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Calendar } from "@/components/ui/calendar";
import { Slider } from "@/components/ui/slider";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import {
  Search,
  Filter,
  X,
  Calendar as CalendarIcon,
  TrendingUp,
  TrendingDown,
  Minus,
  RotateCcw,
  Users,
  AlertTriangle,
} from "lucide-react";
import { format } from "date-fns";
import { ar } from "date-fns/locale";
import { cn } from "@/lib/utils";
import { useRTL } from "@/contexts/rtl-context";
import type { AdvancedFilters, SortOptions } from "@/lib/search-filter-utils";
import { getCollegeFilterOptions } from "@/lib/college-utils";

interface AdvancedSearchFiltersProps {
  filters: Partial<AdvancedFilters>;
  onFiltersChange: (filters: Partial<AdvancedFilters>) => void;
  sortOptions?: SortOptions;
  onSortChange?: (sort: SortOptions) => void;
  resultCount?: number;
  totalCount?: number;
  isLoading?: boolean;
  className?: string;
}

export function AdvancedSearchFilters({
  filters,
  onFiltersChange,
  sortOptions,
  onSortChange,
  resultCount = 0,
  totalCount = 0,
  isLoading = false,
  className,
}: AdvancedSearchFiltersProps) {
  const { isRTL, direction } = useRTL();
  const [showAdvanced, setShowAdvanced] = useState(false);
  const [dateFrom, setDateFrom] = useState<Date | undefined>();
  const [dateTo, setDateTo] = useState<Date | undefined>();

  // Count active filters
  const activeFiltersCount = Object.entries(filters).filter(([key, value]) => {
    if (key === "searchQuery") return value && value.trim() !== "";
    if (key === "year" || key === "college" || key === "gender" || 
        key === "riskLevel" || key === "attendanceStatus" || key === "weeklyTrend") {
      return value && value !== "all";
    }
    return value !== null && value !== undefined;
  }).length;

  const updateFilter = useCallback((key: keyof AdvancedFilters, value: any) => {
    onFiltersChange({ ...filters, [key]: value });
  }, [filters, onFiltersChange]);

  const clearAllFilters = useCallback(() => {
    onFiltersChange({
      searchQuery: "",
      year: "all",
      college: "all",
      gender: "all",
      dateRange: null,
      attendanceRate: null,
      riskLevel: "all",
      attendanceStatus: "all",
      consecutiveAbsences: null,
      currentStreak: null,
      weeklyTrend: "all",
      lastAttendanceWithin: null,
    });
    setDateFrom(undefined);
    setDateTo(undefined);
  }, [onFiltersChange]);

  const handleDateRangeChange = useCallback(() => {
    if (dateFrom && dateTo) {
      updateFilter("dateRange", {
        from: format(dateFrom, "yyyy-MM-dd"),
        to: format(dateTo, "yyyy-MM-dd"),
      });
    } else {
      updateFilter("dateRange", null);
    }
  }, [dateFrom, dateTo, updateFilter]);

  React.useEffect(() => {
    handleDateRangeChange();
  }, [handleDateRangeChange]);

  return (
    <Card className={cn("w-full", className)}>
      <CardHeader className="pb-4">
        <div className={cn(
          "flex items-center justify-between",
          isRTL ? "flex-row-reverse" : "flex-row"
        )}>
          <CardTitle className="flex items-center gap-2">
            <Search className="h-5 w-5" />
            البحث والفلترة المتقدمة
          </CardTitle>
          <div className="flex items-center gap-2">
            {activeFiltersCount > 0 && (
              <Badge variant="secondary" className="gap-1">
                <Filter className="h-3 w-3" />
                {activeFiltersCount} فلتر نشط
              </Badge>
            )}
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowAdvanced(!showAdvanced)}
            >
              <Filter className="h-4 w-4 mr-2" />
              {showAdvanced ? "إخفاء المتقدم" : "إظهار المتقدم"}
            </Button>
          </div>
        </div>
        
        {/* Results summary */}
        <div className="flex items-center justify-between text-sm text-muted-foreground">
          <span>
            {isLoading ? "جاري البحث..." : `${resultCount} من ${totalCount} نتيجة`}
          </span>
          {activeFiltersCount > 0 && (
            <Button
              variant="ghost"
              size="sm"
              onClick={clearAllFilters}
              className="h-auto p-1 text-xs"
            >
              <RotateCcw className="h-3 w-3 mr-1" />
              مسح الكل
            </Button>
          )}
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Basic Search */}
        <div className="relative">
          <Search className={cn(
            "absolute top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground",
            isRTL ? "right-3" : "left-3"
          )} />
          <Input
            placeholder="البحث بالاسم، الرقم، الهاتف، الكلية..."
            value={filters.searchQuery || ""}
            onChange={(e) => updateFilter("searchQuery", e.target.value)}
            className={cn(isRTL ? "pr-10" : "pl-10")}
          />
        </div>

        {/* Quick Filters */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
          <Select
            value={filters.year || "all"}
            onValueChange={(value) => updateFilter("year", value)}
          >
            <SelectTrigger>
              <SelectValue placeholder="السنة" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">جميع السنوات</SelectItem>
              <SelectItem value="1">السنة الأولى</SelectItem>
              <SelectItem value="2">السنة الثانية</SelectItem>
              <SelectItem value="3">السنة الثالثة</SelectItem>
              <SelectItem value="4">السنة الرابعة</SelectItem>
            </SelectContent>
          </Select>

          <Select
            value={filters.college || "all"}
            onValueChange={(value) => updateFilter("college", value)}
          >
            <SelectTrigger>
              <SelectValue placeholder="الكلية" />
            </SelectTrigger>
            <SelectContent>
              {getCollegeFilterOptions().map((option) => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          <Select
            value={filters.gender || "all"}
            onValueChange={(value) => updateFilter("gender", value)}
          >
            <SelectTrigger>
              <SelectValue placeholder="النوع" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">الكل</SelectItem>
              <SelectItem value="male">ذكر</SelectItem>
              <SelectItem value="female">أنثى</SelectItem>
            </SelectContent>
          </Select>

          <Select
            value={filters.riskLevel || "all"}
            onValueChange={(value) => updateFilter("riskLevel", value)}
          >
            <SelectTrigger>
              <SelectValue placeholder="مستوى المخاطر" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">جميع المستويات</SelectItem>
              <SelectItem value="low">منخفض</SelectItem>
              <SelectItem value="medium">متوسط</SelectItem>
              <SelectItem value="high">عالي</SelectItem>
              <SelectItem value="critical">حرج</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Advanced Filters */}
        {showAdvanced && (
          <div className="space-y-4 pt-4 border-t">
            {/* Date Range */}
            <div className="space-y-2">
              <Label>نطاق التاريخ</Label>
              <div className="grid grid-cols-2 gap-2">
                <Popover>
                  <PopoverTrigger asChild>
                    <Button variant="outline" className="justify-start">
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {dateFrom ? format(dateFrom, "dd/MM/yyyy") : "من تاريخ"}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0" align="start">
                    <Calendar
                      mode="single"
                      selected={dateFrom}
                      onSelect={setDateFrom}
                      locale={ar}
                    />
                  </PopoverContent>
                </Popover>

                <Popover>
                  <PopoverTrigger asChild>
                    <Button variant="outline" className="justify-start">
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {dateTo ? format(dateTo, "dd/MM/yyyy") : "إلى تاريخ"}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0" align="start">
                    <Calendar
                      mode="single"
                      selected={dateTo}
                      onSelect={setDateTo}
                      locale={ar}
                    />
                  </PopoverContent>
                </Popover>
              </div>
            </div>

            {/* Attendance Rate Range */}
            <div className="space-y-2">
              <Label>معدل الحضور (%)</Label>
              <div className="px-3">
                <Slider
                  value={filters.attendanceRate ? [filters.attendanceRate.min, filters.attendanceRate.max] : [0, 100]}
                  onValueChange={([min, max]) => updateFilter("attendanceRate", { min, max })}
                  max={100}
                  min={0}
                  step={5}
                  className="w-full"
                />
                <div className="flex justify-between text-xs text-muted-foreground mt-1">
                  <span>{filters.attendanceRate?.min || 0}%</span>
                  <span>{filters.attendanceRate?.max || 100}%</span>
                </div>
              </div>
            </div>

            {/* Additional Filters */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label>حالة الحضور</Label>
                <Select
                  value={filters.attendanceStatus || "all"}
                  onValueChange={(value) => updateFilter("attendanceStatus", value)}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">جميع الحالات</SelectItem>
                    <SelectItem value="active">نشط</SelectItem>
                    <SelectItem value="inactive">غير نشط</SelectItem>
                    <SelectItem value="concerning">يحتاج متابعة</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label>الاتجاه الأسبوعي</Label>
                <Select
                  value={filters.weeklyTrend || "all"}
                  onValueChange={(value) => updateFilter("weeklyTrend", value)}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">جميع الاتجاهات</SelectItem>
                    <SelectItem value="improving">
                      <div className="flex items-center gap-2">
                        <TrendingUp className="h-4 w-4 text-green-500" />
                        تحسن
                      </div>
                    </SelectItem>
                    <SelectItem value="declining">
                      <div className="flex items-center gap-2">
                        <TrendingDown className="h-4 w-4 text-red-500" />
                        تراجع
                      </div>
                    </SelectItem>
                    <SelectItem value="stable">
                      <div className="flex items-center gap-2">
                        <Minus className="h-4 w-4 text-gray-500" />
                        مستقر
                      </div>
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Consecutive Absences */}
            <div className="space-y-2">
              <Label>الغياب المتتالي</Label>
              <div className="px-3">
                <Slider
                  value={filters.consecutiveAbsences ? [filters.consecutiveAbsences.min, filters.consecutiveAbsences.max] : [0, 10]}
                  onValueChange={([min, max]) => updateFilter("consecutiveAbsences", { min, max })}
                  max={10}
                  min={0}
                  step={1}
                  className="w-full"
                />
                <div className="flex justify-between text-xs text-muted-foreground mt-1">
                  <span>{filters.consecutiveAbsences?.min || 0} جلسة</span>
                  <span>{filters.consecutiveAbsences?.max || 10} جلسة</span>
                </div>
              </div>
            </div>

            {/* Last Attendance Within */}
            <div className="space-y-2">
              <Label>آخر حضور خلال (أيام)</Label>
              <Select
                value={filters.lastAttendanceWithin?.toString() || "all"}
                onValueChange={(value) => updateFilter("lastAttendanceWithin", value === "all" ? null : parseInt(value))}
              >
                <SelectTrigger>
                  <SelectValue placeholder="اختر المدة" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">أي وقت</SelectItem>
                  <SelectItem value="7">خلال أسبوع</SelectItem>
                  <SelectItem value="14">خلال أسبوعين</SelectItem>
                  <SelectItem value="30">خلال شهر</SelectItem>
                  <SelectItem value="60">خلال شهرين</SelectItem>
                  <SelectItem value="90">خلال 3 أشهر</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        )}

        {/* Sort Options */}
        {onSortChange && (
          <div className="flex items-center gap-2 pt-4 border-t">
            <Label>ترتيب حسب:</Label>
            <Select
              value={sortOptions?.field || "name"}
              onValueChange={(field) => onSortChange({ 
                field, 
                direction: sortOptions?.direction || "asc" 
              })}
            >
              <SelectTrigger className="w-auto">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="name">الاسم</SelectItem>
                <SelectItem value="attendanceRate">معدل الحضور</SelectItem>
                <SelectItem value="totalSessions">إجمالي الجلسات</SelectItem>
                <SelectItem value="currentStreak">الحضور المتتالي</SelectItem>
                <SelectItem value="lastAttendance">آخر حضور</SelectItem>
                <SelectItem value="riskLevel">مستوى المخاطر</SelectItem>
                <SelectItem value="year">السنة</SelectItem>
              </SelectContent>
            </Select>
            
            <Button
              variant="outline"
              size="sm"
              onClick={() => onSortChange({
                field: sortOptions?.field || "name",
                direction: sortOptions?.direction === "asc" ? "desc" : "asc"
              })}
            >
              {sortOptions?.direction === "asc" ? "تصاعدي" : "تنازلي"}
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
