"use client";

import React, { useState } from "react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import {
  LineChart,
  Line,
  AreaChart,
  Area,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
} from "recharts";
import {
  User,
  Calendar,
  TrendingUp,
  TrendingDown,
  AlertTriangle,
  Award,
  Phone,
  MapPin,
  GraduationCap,
  Building2,
  Clock,
  Target,
  Activity,
  Minus,
} from "lucide-react";
import { format, parseISO } from "date-fns";
import { ar } from "date-fns/locale";
import { cn } from "@/lib/utils";
import { useRTL } from "@/contexts/rtl-context";
import type { UserAttendanceAnalytics } from "@/lib/reports-analytics";
import type { AttendanceRecord } from "@/stores/app-store";

interface UserDetailModalProps {
  userAnalytics: UserAttendanceAnalytics | null;
  attendanceRecords: AttendanceRecord[];
  isOpen: boolean;
  onClose: () => void;
}

export function UserDetailModal({
  userAnalytics,
  attendanceRecords,
  isOpen,
  onClose,
}: UserDetailModalProps) {
  const { isRTL, direction } = useRTL();
  const [activeTab, setActiveTab] = useState("overview");

  if (!userAnalytics) return null;

  const { user } = userAnalytics;

  // Get user's attendance records
  const userRecords = attendanceRecords
    .filter(record => record.user_id === user.id)
    .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());

  // Prepare chart data for monthly attendance
  const monthlyChartData = userAnalytics.monthlyStats.map(stat => ({
    month: stat.month,
    attendanceRate: stat.attendanceRate,
    totalSessions: stat.totalSessions,
    presentSessions: stat.presentSessions,
  }));

  // Prepare recent attendance data for trend chart
  const recentAttendanceData = userRecords.slice(0, 10).reverse().map(record => ({
    date: format(parseISO(record.date), "dd/MM"),
    present: record.present ? 1 : 0,
    dateStr: record.date,
  }));

  const getRiskLevelColor = (level: string) => {
    switch (level) {
      case "low": return "text-green-600 bg-green-100";
      case "medium": return "text-yellow-600 bg-yellow-100";
      case "high": return "text-orange-600 bg-orange-100";
      case "critical": return "text-red-600 bg-red-100";
      default: return "text-gray-600 bg-gray-100";
    }
  };

  const getRiskLevelText = (level: string) => {
    switch (level) {
      case "low": return "منخفض";
      case "medium": return "متوسط";
      case "high": return "عالي";
      case "critical": return "حرج";
      default: return "غير محدد";
    }
  };

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case "improving": return <TrendingUp className="h-4 w-4 text-green-600" />;
      case "declining": return <TrendingDown className="h-4 w-4 text-red-600" />;
      case "stable": return <Minus className="h-4 w-4 text-gray-600" />;
      default: return <Minus className="h-4 w-4 text-gray-600" />;
    }
  };

  const getTrendText = (trend: string) => {
    switch (trend) {
      case "improving": return "تحسن";
      case "declining": return "تراجع";
      case "stable": return "مستقر";
      default: return "غير محدد";
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <div className={cn(
            "flex items-center gap-4",
            isRTL ? "flex-row-reverse" : "flex-row"
          )}>
            <Avatar className="h-16 w-16">
              <AvatarFallback className="text-lg font-bold">
                {user.name.split(' ').map(n => n[0]).join('').substring(0, 2)}
              </AvatarFallback>
            </Avatar>
            <div className={cn("flex-1", isRTL ? "text-right" : "text-left")}>
              <DialogTitle className="text-2xl">{user.name}</DialogTitle>
              <DialogDescription className="text-base">
                {user.user_id} • {user.college} • السنة {user.year}
              </DialogDescription>
            </div>
            <Badge className={cn("px-3 py-1", getRiskLevelColor(userAnalytics.riskLevel))}>
              {getRiskLevelText(userAnalytics.riskLevel)}
            </Badge>
          </div>
        </DialogHeader>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="overview">نظرة عامة</TabsTrigger>
            <TabsTrigger value="attendance">سجل الحضور</TabsTrigger>
            <TabsTrigger value="analytics">التحليلات</TabsTrigger>
            <TabsTrigger value="profile">الملف الشخصي</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-4">
            {/* Key Metrics */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <Card>
                <CardContent className="p-4 text-center">
                  <div className="text-2xl font-bold text-blue-600">
                    {userAnalytics.attendanceRate}%
                  </div>
                  <div className="text-sm text-muted-foreground">معدل الحضور</div>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="p-4 text-center">
                  <div className="text-2xl font-bold text-green-600">
                    {userAnalytics.currentStreak}
                  </div>
                  <div className="text-sm text-muted-foreground">الحضور المتتالي</div>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="p-4 text-center">
                  <div className="text-2xl font-bold text-purple-600">
                    {userAnalytics.longestStreak}
                  </div>
                  <div className="text-sm text-muted-foreground">أطول فترة حضور</div>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="p-4 text-center">
                  <div className="text-2xl font-bold text-orange-600">
                    {userAnalytics.totalSessions}
                  </div>
                  <div className="text-sm text-muted-foreground">إجمالي الجلسات</div>
                </CardContent>
              </Card>
            </div>

            {/* Weekly Trend */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Activity className="h-5 w-5" />
                  الاتجاه الأسبوعي
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    {getTrendIcon(userAnalytics.weeklyTrend.trend)}
                    <span className="font-medium">
                      {getTrendText(userAnalytics.weeklyTrend.trend)}
                    </span>
                  </div>
                  <div className="text-right">
                    <div className="text-sm text-muted-foreground">التغيير الأسبوعي</div>
                    <div className={cn(
                      "font-bold",
                      userAnalytics.weeklyTrend.changePercentage > 0 ? "text-green-600" : 
                      userAnalytics.weeklyTrend.changePercentage < 0 ? "text-red-600" : "text-gray-600"
                    )}>
                      {userAnalytics.weeklyTrend.changePercentage > 0 ? "+" : ""}
                      {userAnalytics.weeklyTrend.changePercentage}%
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Recent Attendance Trend */}
            <Card>
              <CardHeader>
                <CardTitle>اتجاه الحضور الأخير</CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={200}>
                  <LineChart data={recentAttendanceData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="date" />
                    <YAxis domain={[0, 1]} tickFormatter={(value) => value === 1 ? "حاضر" : "غائب"} />
                    <Tooltip 
                      formatter={(value: any, name: string) => [
                        value === 1 ? "حاضر" : "غائب", 
                        "الحالة"
                      ]}
                      labelFormatter={(label: string, payload: any) => {
                        if (payload && payload[0]) {
                          return format(parseISO(payload[0].payload.dateStr), "EEEE، dd MMMM yyyy", { locale: ar });
                        }
                        return label;
                      }}
                    />
                    <Line 
                      type="monotone" 
                      dataKey="present" 
                      stroke="#3b82f6" 
                      strokeWidth={2}
                      dot={{ fill: "#3b82f6", strokeWidth: 2, r: 4 }}
                    />
                  </LineChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            {/* Absence Pattern */}
            {userAnalytics.absencePattern.consecutiveAbsences > 0 && (
              <Card className="border-orange-200 bg-orange-50">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2 text-orange-700">
                    <AlertTriangle className="h-5 w-5" />
                    تنبيه: نمط الغياب
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <p className="text-orange-700">
                      غياب متتالي لمدة {userAnalytics.absencePattern.consecutiveAbsences} جلسات
                    </p>
                    {userAnalytics.absencePattern.frequentAbsenceDays.length > 0 && (
                      <p className="text-sm text-orange-600">
                        أيام الغياب المتكررة: {userAnalytics.absencePattern.frequentAbsenceDays.join("، ")}
                      </p>
                    )}
                  </div>
                </CardContent>
              </Card>
            )}
          </TabsContent>

          <TabsContent value="attendance" className="space-y-4">
            {/* Recent Attendance Records */}
            <Card>
              <CardHeader>
                <CardTitle>سجل الحضور الأخير</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2 max-h-96 overflow-y-auto">
                  {userRecords.slice(0, 20).map((record) => (
                    <div
                      key={record.id}
                      className={cn(
                        "flex items-center justify-between p-3 rounded-lg border",
                        record.present ? "bg-green-50 border-green-200" : "bg-red-50 border-red-200"
                      )}
                    >
                      <div className="flex items-center gap-3">
                        <div className={cn(
                          "w-3 h-3 rounded-full",
                          record.present ? "bg-green-500" : "bg-red-500"
                        )} />
                        <div>
                          <div className="font-medium">
                            {format(parseISO(record.date), "EEEE، dd MMMM yyyy", { locale: ar })}
                          </div>
                          <div className="text-sm text-muted-foreground">
                            سجل بواسطة: {record.marked_by}
                          </div>
                        </div>
                      </div>
                      <Badge variant={record.present ? "default" : "destructive"}>
                        {record.present ? "حاضر" : "غائب"}
                      </Badge>
                    </div>
                  ))}
                  {userRecords.length === 0 && (
                    <div className="text-center py-8 text-muted-foreground">
                      لا توجد سجلات حضور
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="analytics" className="space-y-4">
            {/* Monthly Performance */}
            <Card>
              <CardHeader>
                <CardTitle>الأداء الشهري</CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={250}>
                  <AreaChart data={monthlyChartData}>
                    <defs>
                      <linearGradient id="attendanceGradient" x1="0" y1="0" x2="0" y2="1">
                        <stop offset="5%" stopColor="#3b82f6" stopOpacity={0.8}/>
                        <stop offset="95%" stopColor="#3b82f6" stopOpacity={0.1}/>
                      </linearGradient>
                    </defs>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="month" />
                    <YAxis />
                    <Tooltip 
                      formatter={(value: any, name: string) => [
                        `${value}%`, 
                        "معدل الحضور"
                      ]}
                    />
                    <Area
                      type="monotone"
                      dataKey="attendanceRate"
                      stroke="#3b82f6"
                      fillOpacity={1}
                      fill="url(#attendanceGradient)"
                    />
                  </AreaChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            {/* Monthly Stats Table */}
            <Card>
              <CardHeader>
                <CardTitle>الإحصائيات الشهرية</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="overflow-x-auto">
                  <table className="w-full text-sm">
                    <thead>
                      <tr className="border-b">
                        <th className="text-right p-2">الشهر</th>
                        <th className="text-right p-2">إجمالي الجلسات</th>
                        <th className="text-right p-2">الحضور</th>
                        <th className="text-right p-2">المعدل</th>
                      </tr>
                    </thead>
                    <tbody>
                      {userAnalytics.monthlyStats.map((stat) => (
                        <tr key={`${stat.month}-${stat.year}`} className="border-b">
                          <td className="p-2 font-medium">{stat.month} {stat.year}</td>
                          <td className="p-2">{stat.totalSessions}</td>
                          <td className="p-2">{stat.presentSessions}</td>
                          <td className="p-2">
                            <Badge variant={stat.attendanceRate >= 70 ? "default" : "destructive"}>
                              {stat.attendanceRate}%
                            </Badge>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="profile" className="space-y-4">
            {/* Personal Information */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <User className="h-5 w-5" />
                  المعلومات الشخصية
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-3">
                    <div className="flex items-center gap-2">
                      <User className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm text-muted-foreground">الاسم:</span>
                      <span className="font-medium">{user.name}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Phone className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm text-muted-foreground">الهاتف:</span>
                      <span className="font-medium">{user.phone}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Calendar className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm text-muted-foreground">تاريخ الميلاد:</span>
                      <span className="font-medium">
                        {format(parseISO(user.birthdate), "dd MMMM yyyy", { locale: ar })}
                      </span>
                    </div>
                    <div className="flex items-center gap-2">
                      <MapPin className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm text-muted-foreground">العنوان:</span>
                      <span className="font-medium">{user.address}</span>
                    </div>
                  </div>
                  <div className="space-y-3">
                    <div className="flex items-center gap-2">
                      <GraduationCap className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm text-muted-foreground">السنة:</span>
                      <span className="font-medium">السنة {user.year}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Building2 className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm text-muted-foreground">الكلية:</span>
                      <span className="font-medium">{user.college}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <GraduationCap className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm text-muted-foreground">القسم:</span>
                      <span className="font-medium">{user.department}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Clock className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm text-muted-foreground">أول حضور:</span>
                      <span className="font-medium">
                        {format(parseISO(user.first_attendance_date), "dd MMMM yyyy", { locale: ar })}
                      </span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Last Attendance */}
            {userAnalytics.lastAttendance && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Target className="h-5 w-5" />
                    آخر حضور
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-center p-4">
                    <div className="text-2xl font-bold text-green-600">
                      {format(parseISO(userAnalytics.lastAttendance), "dd MMMM yyyy", { locale: ar })}
                    </div>
                    <div className="text-sm text-muted-foreground">
                      {format(parseISO(userAnalytics.lastAttendance), "EEEE", { locale: ar })}
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}
          </TabsContent>
        </Tabs>
      </DialogContent>
    </Dialog>
  );
}
