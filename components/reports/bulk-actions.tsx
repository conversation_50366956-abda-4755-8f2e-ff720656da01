"use client";

import React, { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import { Badge } from "@/components/ui/badge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  CheckSquare,
  Square,
  MoreVertical,
  Mail,
  MessageSquare,
  UserPlus,
  UserMinus,
  AlertTriangle,
  Download,
  Filter,
  Users,
  Calendar,
  Send,
  FileText,
} from "lucide-react";
import { cn } from "@/lib/utils";
import { useRTL } from "@/contexts/rtl-context";
import { toast } from "sonner";
import type { UserAttendanceAnalytics } from "@/lib/reports-analytics";
import { exportComprehensiveReport } from "@/lib/data-export-import";

interface BulkActionsProps {
  userAnalytics: UserAttendanceAnalytics[];
  selectedUsers: string[];
  onSelectionChange: (selectedIds: string[]) => void;
  onUserUpdate?: (userIds: string[], action: string, data?: any) => void;
  className?: string;
}

export function BulkActions({
  userAnalytics,
  selectedUsers,
  onSelectionChange,
  onUserUpdate,
  className,
}: BulkActionsProps) {
  const { isRTL, direction } = useRTL();
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);
  const [pendingAction, setPendingAction] = useState<{
    type: string;
    title: string;
    description: string;
    data?: any;
  } | null>(null);
  const [showMessageDialog, setShowMessageDialog] = useState(false);
  const [messageContent, setMessageContent] = useState("");
  const [messageType, setMessageType] = useState<"email" | "whatsapp">("whatsapp");

  const selectedAnalytics = userAnalytics.filter(ua => 
    selectedUsers.includes(ua.user.id)
  );

  const allSelected = userAnalytics.length > 0 && selectedUsers.length === userAnalytics.length;
  const someSelected = selectedUsers.length > 0 && selectedUsers.length < userAnalytics.length;

  const handleSelectAll = () => {
    if (allSelected) {
      onSelectionChange([]);
    } else {
      onSelectionChange(userAnalytics.map(ua => ua.user.id));
    }
  };

  const handleBulkAction = (action: string, title: string, description: string, data?: any) => {
    if (selectedUsers.length === 0) {
      toast.error("يرجى اختيار أعضاء أولاً");
      return;
    }

    setPendingAction({ type: action, title, description, data });
    setShowConfirmDialog(true);
  };

  const executeBulkAction = async () => {
    if (!pendingAction || !onUserUpdate) return;

    try {
      await onUserUpdate(selectedUsers, pendingAction.type, pendingAction.data);
      toast.success(`تم تنفيذ العملية على ${selectedUsers.length} عضو بنجاح`);
      onSelectionChange([]);
    } catch (error) {
      toast.error("حدث خطأ أثناء تنفيذ العملية");
      console.error("Bulk action error:", error);
    } finally {
      setShowConfirmDialog(false);
      setPendingAction(null);
    }
  };

  const handleSendMessage = async () => {
    if (selectedUsers.length === 0) {
      toast.error("يرجى اختيار أعضاء أولاً");
      return;
    }

    if (!messageContent.trim()) {
      toast.error("يرجى كتابة محتوى الرسالة");
      return;
    }

    try {
      // Here you would integrate with actual messaging service
      console.log(`Sending ${messageType} message to ${selectedUsers.length} users:`, messageContent);
      toast.success(`تم إرسال الرسالة إلى ${selectedUsers.length} عضو`);
      setShowMessageDialog(false);
      setMessageContent("");
    } catch (error) {
      toast.error("حدث خطأ أثناء إرسال الرسالة");
    }
  };

  const handleExportSelected = async () => {
    if (selectedUsers.length === 0) {
      toast.error("يرجى اختيار أعضاء أولاً");
      return;
    }

    try {
      const selectedData = {
        summary: {
          totalUsers: selectedAnalytics.length,
          totalSessions: selectedAnalytics.reduce((sum, ua) => sum + ua.totalSessions, 0),
          overallAttendanceRate: Math.round(
            selectedAnalytics.reduce((sum, ua) => sum + ua.attendanceRate, 0) / selectedAnalytics.length
          ),
          activeUsers: selectedAnalytics.filter(ua => ua.totalSessions > 0).length,
          inactiveUsers: selectedAnalytics.filter(ua => ua.totalSessions === 0).length,
        },
        userAnalytics: selectedAnalytics,
        trends: [],
        collegeComparison: [],
        timeComparison: {
          currentPeriod: { startDate: "", endDate: "", totalSessions: 0, averageAttendance: 0, topPerformers: [], concerningUsers: [] },
          previousPeriod: { startDate: "", endDate: "", totalSessions: 0, averageAttendance: 0, topPerformers: [], concerningUsers: [] },
          improvement: 0,
          trend: "stable" as const,
        },
        alerts: [],
      };

      await exportComprehensiveReport(selectedData, "excel", `selected_users_report_${new Date().toISOString().split('T')[0]}`);
      toast.success("تم تصدير بيانات الأعضاء المحددين بنجاح");
    } catch (error) {
      toast.error("حدث خطأ أثناء التصدير");
    }
  };

  // Calculate selection statistics
  const selectionStats = {
    total: selectedUsers.length,
    highRisk: selectedAnalytics.filter(ua => ua.riskLevel === "high" || ua.riskLevel === "critical").length,
    lowAttendance: selectedAnalytics.filter(ua => ua.attendanceRate < 50).length,
    inactive: selectedAnalytics.filter(ua => ua.totalSessions === 0).length,
    avgAttendance: selectedAnalytics.length > 0 
      ? Math.round(selectedAnalytics.reduce((sum, ua) => sum + ua.attendanceRate, 0) / selectedAnalytics.length)
      : 0,
  };

  return (
    <>
      <Card className={className}>
        <CardHeader className="pb-3">
          <div className={cn(
            "flex items-center justify-between",
            isRTL ? "flex-row-reverse" : "flex-row"
          )}>
            <CardTitle className="flex items-center gap-2">
              <Users className="h-5 w-5" />
              العمليات المجمعة
            </CardTitle>
            <div className="flex items-center gap-2">
              <Checkbox
                checked={allSelected}
                ref={(el) => {
                  if (el) el.indeterminate = someSelected;
                }}
                onCheckedChange={handleSelectAll}
              />
              <span className="text-sm text-muted-foreground">
                تحديد الكل ({userAnalytics.length})
              </span>
            </div>
          </div>
        </CardHeader>

        <CardContent className="space-y-4">
          {/* Selection Summary */}
          {selectedUsers.length > 0 && (
            <div className="p-3 bg-blue-50 rounded-lg border border-blue-200">
              <div className="flex items-center justify-between mb-2">
                <span className="font-medium text-blue-900">
                  تم تحديد {selectedUsers.length} عضو
                </span>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => onSelectionChange([])}
                  className="text-blue-700 hover:text-blue-900"
                >
                  إلغاء التحديد
                </Button>
              </div>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-2 text-sm">
                <div className="text-center">
                  <div className="font-bold text-blue-700">{selectionStats.avgAttendance}%</div>
                  <div className="text-blue-600">متوسط الحضور</div>
                </div>
                <div className="text-center">
                  <div className="font-bold text-red-700">{selectionStats.highRisk}</div>
                  <div className="text-red-600">عالي المخاطر</div>
                </div>
                <div className="text-center">
                  <div className="font-bold text-orange-700">{selectionStats.lowAttendance}</div>
                  <div className="text-orange-600">حضور منخفض</div>
                </div>
                <div className="text-center">
                  <div className="font-bold text-gray-700">{selectionStats.inactive}</div>
                  <div className="text-gray-600">غير نشط</div>
                </div>
              </div>
            </div>
          )}

          {/* Action Buttons */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowMessageDialog(true)}
              disabled={selectedUsers.length === 0}
              className="justify-start"
            >
              <MessageSquare className="h-4 w-4 mr-2" />
              إرسال رسالة
            </Button>

            <Button
              variant="outline"
              size="sm"
              onClick={handleExportSelected}
              disabled={selectedUsers.length === 0}
              className="justify-start"
            >
              <Download className="h-4 w-4 mr-2" />
              تصدير المحدد
            </Button>

            <Button
              variant="outline"
              size="sm"
              onClick={() => handleBulkAction(
                "mark_for_followup",
                "وضع علامة للمتابعة",
                `سيتم وضع علامة للمتابعة على ${selectedUsers.length} عضو`
              )}
              disabled={selectedUsers.length === 0}
              className="justify-start"
            >
              <AlertTriangle className="h-4 w-4 mr-2" />
              متابعة
            </Button>

            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="outline"
                  size="sm"
                  disabled={selectedUsers.length === 0}
                  className="justify-start"
                >
                  <MoreVertical className="h-4 w-4 mr-2" />
                  المزيد
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem
                  onClick={() => handleBulkAction(
                    "add_to_group",
                    "إضافة إلى مجموعة",
                    `سيتم إضافة ${selectedUsers.length} عضو إلى مجموعة خاصة`
                  )}
                >
                  <UserPlus className="h-4 w-4 mr-2" />
                  إضافة إلى مجموعة
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={() => handleBulkAction(
                    "schedule_reminder",
                    "جدولة تذكير",
                    `سيتم جدولة تذكير للحضور لـ ${selectedUsers.length} عضو`
                  )}
                >
                  <Calendar className="h-4 w-4 mr-2" />
                  جدولة تذكير
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem
                  onClick={() => handleBulkAction(
                    "generate_report",
                    "إنشاء تقرير مخصص",
                    `سيتم إنشاء تقرير مفصل للأعضاء المحددين`
                  )}
                >
                  <FileText className="h-4 w-4 mr-2" />
                  تقرير مخصص
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>

          {/* Quick Filters */}
          <div className="space-y-2">
            <Label className="text-sm font-medium">تحديد سريع</Label>
            <div className="flex flex-wrap gap-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => {
                  const highRiskUsers = userAnalytics
                    .filter(ua => ua.riskLevel === "high" || ua.riskLevel === "critical")
                    .map(ua => ua.user.id);
                  onSelectionChange(highRiskUsers);
                }}
                className="text-xs"
              >
                عالي المخاطر ({userAnalytics.filter(ua => ua.riskLevel === "high" || ua.riskLevel === "critical").length})
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => {
                  const lowAttendanceUsers = userAnalytics
                    .filter(ua => ua.attendanceRate < 50)
                    .map(ua => ua.user.id);
                  onSelectionChange(lowAttendanceUsers);
                }}
                className="text-xs"
              >
                حضور منخفض ({userAnalytics.filter(ua => ua.attendanceRate < 50).length})
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => {
                  const inactiveUsers = userAnalytics
                    .filter(ua => ua.totalSessions === 0)
                    .map(ua => ua.user.id);
                  onSelectionChange(inactiveUsers);
                }}
                className="text-xs"
              >
                غير نشط ({userAnalytics.filter(ua => ua.totalSessions === 0).length})
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => {
                  const decliningUsers = userAnalytics
                    .filter(ua => ua.weeklyTrend.trend === "declining")
                    .map(ua => ua.user.id);
                  onSelectionChange(decliningUsers);
                }}
                className="text-xs"
              >
                اتجاه متراجع ({userAnalytics.filter(ua => ua.weeklyTrend.trend === "declining").length})
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Confirmation Dialog */}
      <AlertDialog open={showConfirmDialog} onOpenChange={setShowConfirmDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>{pendingAction?.title}</AlertDialogTitle>
            <AlertDialogDescription>
              {pendingAction?.description}
              <br />
              هل أنت متأكد من المتابعة؟
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>إلغاء</AlertDialogCancel>
            <AlertDialogAction onClick={executeBulkAction}>
              تأكيد
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Message Dialog */}
      <AlertDialog open={showMessageDialog} onOpenChange={setShowMessageDialog}>
        <AlertDialogContent className="max-w-md">
          <AlertDialogHeader>
            <AlertDialogTitle>إرسال رسالة جماعية</AlertDialogTitle>
            <AlertDialogDescription>
              سيتم إرسال الرسالة إلى {selectedUsers.length} عضو
            </AlertDialogDescription>
          </AlertDialogHeader>
          <div className="space-y-4">
            <div className="space-y-2">
              <Label>نوع الرسالة</Label>
              <Select value={messageType} onValueChange={(value: "email" | "whatsapp") => setMessageType(value)}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="whatsapp">
                    <div className="flex items-center gap-2">
                      <MessageSquare className="h-4 w-4" />
                      واتساب
                    </div>
                  </SelectItem>
                  <SelectItem value="email">
                    <div className="flex items-center gap-2">
                      <Mail className="h-4 w-4" />
                      بريد إلكتروني
                    </div>
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label>محتوى الرسالة</Label>
              <Textarea
                value={messageContent}
                onChange={(e) => setMessageContent(e.target.value)}
                placeholder="اكتب رسالتك هنا..."
                rows={4}
              />
            </div>
          </div>
          <AlertDialogFooter>
            <AlertDialogCancel>إلغاء</AlertDialogCancel>
            <AlertDialogAction onClick={handleSendMessage}>
              <Send className="h-4 w-4 mr-2" />
              إرسال
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}
