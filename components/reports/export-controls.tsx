"use client";

import React, { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Download,
  FileText,
  FileSpreadsheet,
  File,
  Settings,
  Calendar,
  Users,
  BarChart3,
  AlertTriangle,
} from "lucide-react";
import { format } from "date-fns";
import { ar } from "date-fns/locale";
import { cn } from "@/lib/utils";
import { useRTL } from "@/contexts/rtl-context";
import { toast } from "sonner";
import { exportComprehensiveReport, type ExportFormat } from "@/lib/data-export-import";
import type { DetailedReportData } from "@/lib/reports-analytics";

interface ExportControlsProps {
  reportData: DetailedReportData;
  className?: string;
}

interface ExportOptions {
  format: ExportFormat;
  filename: string;
  includeCharts: boolean;
  includeSummary: boolean;
  includeUserDetails: boolean;
  includeAlerts: boolean;
  includeCollegeComparison: boolean;
  dateRange: string;
}

export function ExportControls({ reportData, className }: ExportControlsProps) {
  const { isRTL, direction } = useRTL();
  const [isExporting, setIsExporting] = useState(false);
  const [showAdvancedOptions, setShowAdvancedOptions] = useState(false);
  const [exportOptions, setExportOptions] = useState<ExportOptions>({
    format: "pdf",
    filename: `attendance_report_${format(new Date(), "yyyy-MM-dd")}`,
    includeCharts: true,
    includeSummary: true,
    includeUserDetails: true,
    includeAlerts: true,
    includeCollegeComparison: true,
    dateRange: "current",
  });

  const handleQuickExport = async (format: ExportFormat) => {
    setIsExporting(true);
    try {
      await exportComprehensiveReport(reportData, format);
      toast.success(`تم تصدير التقرير بصيغة ${getFormatLabel(format)} بنجاح`);
    } catch (error) {
      toast.error("حدث خطأ أثناء تصدير التقرير");
      console.error("Export error:", error);
    } finally {
      setIsExporting(false);
    }
  };

  const handleAdvancedExport = async () => {
    setIsExporting(true);
    try {
      // Create filtered report data based on options
      const filteredData = {
        ...reportData,
        summary: exportOptions.includeSummary ? reportData.summary : undefined,
        userAnalytics: exportOptions.includeUserDetails ? reportData.userAnalytics : [],
        alerts: exportOptions.includeAlerts ? reportData.alerts : [],
        collegeComparison: exportOptions.includeCollegeComparison ? reportData.collegeComparison : [],
      };

      await exportComprehensiveReport(filteredData, exportOptions.format, exportOptions.filename);
      toast.success(`تم تصدير التقرير المخصص بنجاح`);
      setShowAdvancedOptions(false);
    } catch (error) {
      toast.error("حدث خطأ أثناء تصدير التقرير");
      console.error("Export error:", error);
    } finally {
      setIsExporting(false);
    }
  };

  const getFormatLabel = (format: ExportFormat) => {
    switch (format) {
      case "pdf": return "PDF";
      case "excel": return "Excel";
      case "csv": return "CSV";
      default: return format;
    }
  };

  const getFormatIcon = (format: ExportFormat) => {
    switch (format) {
      case "pdf": return <FileText className="h-4 w-4" />;
      case "excel": return <FileSpreadsheet className="h-4 w-4" />;
      case "csv": return <File className="h-4 w-4" />;
      default: return <File className="h-4 w-4" />;
    }
  };

  const exportStats = {
    totalUsers: reportData.userAnalytics.length,
    totalSessions: reportData.summary.totalSessions,
    alertsCount: reportData.alerts.length,
    chartsCount: 5, // Number of available charts
  };

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Download className="h-5 w-5" />
          تصدير التقارير
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Quick Export Buttons */}
        <div className="space-y-3">
          <Label className="text-sm font-medium">تصدير سريع</Label>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-2">
            <Button
              variant="outline"
              onClick={() => handleQuickExport("pdf")}
              disabled={isExporting}
              className="justify-start"
            >
              <FileText className="h-4 w-4 mr-2" />
              PDF
            </Button>
            <Button
              variant="outline"
              onClick={() => handleQuickExport("excel")}
              disabled={isExporting}
              className="justify-start"
            >
              <FileSpreadsheet className="h-4 w-4 mr-2" />
              Excel
            </Button>
            <Button
              variant="outline"
              onClick={() => handleQuickExport("csv")}
              disabled={isExporting}
              className="justify-start"
            >
              <File className="h-4 w-4 mr-2" />
              CSV
            </Button>
          </div>
        </div>

        {/* Export Statistics */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-3 p-3 bg-muted rounded-lg">
          <div className="text-center">
            <div className="flex items-center justify-center gap-1 text-blue-600">
              <Users className="h-4 w-4" />
              <span className="font-bold">{exportStats.totalUsers}</span>
            </div>
            <div className="text-xs text-muted-foreground">عضو</div>
          </div>
          <div className="text-center">
            <div className="flex items-center justify-center gap-1 text-green-600">
              <Calendar className="h-4 w-4" />
              <span className="font-bold">{exportStats.totalSessions}</span>
            </div>
            <div className="text-xs text-muted-foreground">جلسة</div>
          </div>
          <div className="text-center">
            <div className="flex items-center justify-center gap-1 text-orange-600">
              <AlertTriangle className="h-4 w-4" />
              <span className="font-bold">{exportStats.alertsCount}</span>
            </div>
            <div className="text-xs text-muted-foreground">تنبيه</div>
          </div>
          <div className="text-center">
            <div className="flex items-center justify-center gap-1 text-purple-600">
              <BarChart3 className="h-4 w-4" />
              <span className="font-bold">{exportStats.chartsCount}</span>
            </div>
            <div className="text-xs text-muted-foreground">رسم بياني</div>
          </div>
        </div>

        {/* Advanced Export Options */}
        <Dialog open={showAdvancedOptions} onOpenChange={setShowAdvancedOptions}>
          <DialogTrigger asChild>
            <Button variant="outline" className="w-full">
              <Settings className="h-4 w-4 mr-2" />
              خيارات التصدير المتقدمة
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-md">
            <DialogHeader>
              <DialogTitle>خيارات التصدير المتقدمة</DialogTitle>
              <DialogDescription>
                اختر المحتوى والصيغة المطلوبة للتقرير
              </DialogDescription>
            </DialogHeader>

            <div className="space-y-4">
              {/* Format Selection */}
              <div className="space-y-2">
                <Label>صيغة التصدير</Label>
                <Select
                  value={exportOptions.format}
                  onValueChange={(value: ExportFormat) =>
                    setExportOptions({ ...exportOptions, format: value })
                  }
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="pdf">
                      <div className="flex items-center gap-2">
                        <FileText className="h-4 w-4" />
                        PDF - تقرير مفصل
                      </div>
                    </SelectItem>
                    <SelectItem value="excel">
                      <div className="flex items-center gap-2">
                        <FileSpreadsheet className="h-4 w-4" />
                        Excel - جداول بيانات
                      </div>
                    </SelectItem>
                    <SelectItem value="csv">
                      <div className="flex items-center gap-2">
                        <File className="h-4 w-4" />
                        CSV - بيانات خام
                      </div>
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Filename */}
              <div className="space-y-2">
                <Label>اسم الملف</Label>
                <Input
                  value={exportOptions.filename}
                  onChange={(e) =>
                    setExportOptions({ ...exportOptions, filename: e.target.value })
                  }
                  placeholder="اسم الملف"
                />
              </div>

              {/* Content Options */}
              <div className="space-y-3">
                <Label>المحتوى المطلوب</Label>
                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="summary"
                      checked={exportOptions.includeSummary}
                      onCheckedChange={(checked) =>
                        setExportOptions({ ...exportOptions, includeSummary: !!checked })
                      }
                    />
                    <Label htmlFor="summary" className="text-sm">
                      الملخص التنفيذي
                    </Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="userDetails"
                      checked={exportOptions.includeUserDetails}
                      onCheckedChange={(checked) =>
                        setExportOptions({ ...exportOptions, includeUserDetails: !!checked })
                      }
                    />
                    <Label htmlFor="userDetails" className="text-sm">
                      تفاصيل الأعضاء
                    </Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="alerts"
                      checked={exportOptions.includeAlerts}
                      onCheckedChange={(checked) =>
                        setExportOptions({ ...exportOptions, includeAlerts: !!checked })
                      }
                    />
                    <Label htmlFor="alerts" className="text-sm">
                      التنبيهات والتحذيرات
                    </Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="collegeComparison"
                      checked={exportOptions.includeCollegeComparison}
                      onCheckedChange={(checked) =>
                        setExportOptions({ ...exportOptions, includeCollegeComparison: !!checked })
                      }
                    />
                    <Label htmlFor="collegeComparison" className="text-sm">
                      مقارنة الكليات
                    </Label>
                  </div>
                  {exportOptions.format === "pdf" && (
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="charts"
                        checked={exportOptions.includeCharts}
                        onCheckedChange={(checked) =>
                          setExportOptions({ ...exportOptions, includeCharts: !!checked })
                        }
                      />
                      <Label htmlFor="charts" className="text-sm">
                        الرسوم البيانية
                      </Label>
                    </div>
                  )}
                </div>
              </div>
            </div>

            <DialogFooter>
              <Button
                variant="outline"
                onClick={() => setShowAdvancedOptions(false)}
              >
                إلغاء
              </Button>
              <Button
                onClick={handleAdvancedExport}
                disabled={isExporting}
              >
                {isExporting ? "جاري التصدير..." : "تصدير"}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {/* Export Templates */}
        <div className="space-y-2">
          <Label className="text-sm font-medium">قوالب جاهزة</Label>
          <div className="grid grid-cols-1 gap-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => {
                setExportOptions({
                  ...exportOptions,
                  format: "pdf",
                  includeSummary: true,
                  includeUserDetails: false,
                  includeAlerts: true,
                  includeCollegeComparison: true,
                  includeCharts: true,
                });
                setShowAdvancedOptions(true);
              }}
              className="justify-start text-xs"
            >
              📊 تقرير إداري مختصر
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => {
                setExportOptions({
                  ...exportOptions,
                  format: "excel",
                  includeSummary: true,
                  includeUserDetails: true,
                  includeAlerts: true,
                  includeCollegeComparison: true,
                  includeCharts: false,
                });
                setShowAdvancedOptions(true);
              }}
              className="justify-start text-xs"
            >
              📋 تقرير تفصيلي شامل
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => {
                setExportOptions({
                  ...exportOptions,
                  format: "csv",
                  includeSummary: false,
                  includeUserDetails: true,
                  includeAlerts: false,
                  includeCollegeComparison: false,
                  includeCharts: false,
                });
                setShowAdvancedOptions(true);
              }}
              className="justify-start text-xs"
            >
              📈 بيانات خام للتحليل
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
