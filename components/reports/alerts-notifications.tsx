"use client";

import React, { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  AlertTriangle,
  Bell,
  BellOff,
  CheckCircle,
  XCircle,
  Clock,
  TrendingDown,
  UserX,
  Settings,
  Send,
  Eye,
  EyeOff,
} from "lucide-react";
import { format } from "date-fns";
import { ar } from "date-fns/locale";
import { cn } from "@/lib/utils";
import { useRTL } from "@/contexts/rtl-context";
import { toast } from "sonner";
import type { Attendance<PERSON><PERSON>t, UserAttendanceAnalytics } from "@/lib/reports-analytics";

interface AlertsNotificationsProps {
  alerts: AttendanceAlert[];
  userAnalytics: UserAttendanceAnalytics[];
  onAlertAction?: (alertId: string, action: "dismiss" | "resolve" | "snooze") => void;
  onNotificationSend?: (alertId: string, method: "email" | "whatsapp") => void;
  className?: string;
}

interface NotificationSettings {
  enabled: boolean;
  absenceStreakThreshold: number;
  lowAttendanceThreshold: number;
  inactivityDays: number;
  autoNotify: boolean;
  notificationMethods: ("email" | "whatsapp")[];
}

export function AlertsNotifications({
  alerts,
  userAnalytics,
  onAlertAction,
  onNotificationSend,
  className,
}: AlertsNotificationsProps) {
  const { isRTL, direction } = useRTL();
  const [showSettings, setShowSettings] = useState(false);
  const [dismissedAlerts, setDismissedAlerts] = useState<Set<string>>(new Set());
  const [settings, setSettings] = useState<NotificationSettings>({
    enabled: true,
    absenceStreakThreshold: 3,
    lowAttendanceThreshold: 50,
    inactivityDays: 30,
    autoNotify: false,
    notificationMethods: ["whatsapp"],
  });

  // Filter out dismissed alerts
  const visibleAlerts = alerts.filter(alert => !dismissedAlerts.has(alert.id));

  // Group alerts by severity
  const alertsBySeverity = {
    critical: visibleAlerts.filter(a => a.severity === "critical"),
    high: visibleAlerts.filter(a => a.severity === "high"),
    medium: visibleAlerts.filter(a => a.severity === "medium"),
    low: visibleAlerts.filter(a => a.severity === "low"),
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case "critical": return "text-red-700 bg-red-100 border-red-200";
      case "high": return "text-orange-700 bg-orange-100 border-orange-200";
      case "medium": return "text-yellow-700 bg-yellow-100 border-yellow-200";
      case "low": return "text-blue-700 bg-blue-100 border-blue-200";
      default: return "text-gray-700 bg-gray-100 border-gray-200";
    }
  };

  const getSeverityIcon = (severity: string) => {
    switch (severity) {
      case "critical": return <AlertTriangle className="h-4 w-4 text-red-600" />;
      case "high": return <AlertTriangle className="h-4 w-4 text-orange-600" />;
      case "medium": return <Clock className="h-4 w-4 text-yellow-600" />;
      case "low": return <Bell className="h-4 w-4 text-blue-600" />;
      default: return <Bell className="h-4 w-4 text-gray-600" />;
    }
  };

  const getAlertTypeIcon = (type: string) => {
    switch (type) {
      case "absence_streak": return <XCircle className="h-4 w-4" />;
      case "declining_trend": return <TrendingDown className="h-4 w-4" />;
      case "low_attendance": return <AlertTriangle className="h-4 w-4" />;
      case "inactive_user": return <UserX className="h-4 w-4" />;
      default: return <Bell className="h-4 w-4" />;
    }
  };

  const getSeverityText = (severity: string) => {
    switch (severity) {
      case "critical": return "حرج";
      case "high": return "عالي";
      case "medium": return "متوسط";
      case "low": return "منخفض";
      default: return "غير محدد";
    }
  };

  const getAlertTypeText = (type: string) => {
    switch (type) {
      case "absence_streak": return "غياب متتالي";
      case "declining_trend": return "اتجاه متراجع";
      case "low_attendance": return "حضور منخفض";
      case "inactive_user": return "عضو غير نشط";
      default: return "تنبيه عام";
    }
  };

  const handleAlertAction = (alertId: string, action: "dismiss" | "resolve" | "snooze") => {
    if (action === "dismiss") {
      setDismissedAlerts(prev => new Set([...prev, alertId]));
      toast.success("تم إخفاء التنبيه");
    }
    
    if (onAlertAction) {
      onAlertAction(alertId, action);
    }
  };

  const handleSendNotification = (alertId: string, method: "email" | "whatsapp") => {
    if (onNotificationSend) {
      onNotificationSend(alertId, method);
      toast.success(`تم إرسال إشعار عبر ${method === "email" ? "البريد الإلكتروني" : "واتساب"}`);
    }
  };

  const alertsCount = {
    total: visibleAlerts.length,
    critical: alertsBySeverity.critical.length,
    high: alertsBySeverity.high.length,
    actionRequired: visibleAlerts.filter(a => a.actionRequired).length,
  };

  return (
    <div className={cn("space-y-4", className)}>
      {/* Alerts Summary */}
      <Card>
        <CardHeader>
          <div className={cn(
            "flex items-center justify-between",
            isRTL ? "flex-row-reverse" : "flex-row"
          )}>
            <CardTitle className="flex items-center gap-2">
              <Bell className="h-5 w-5" />
              التنبيهات والإشعارات
            </CardTitle>
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowSettings(!showSettings)}
              >
                <Settings className="h-4 w-4 mr-2" />
                الإعدادات
              </Button>
              <Badge variant={alertsCount.critical > 0 ? "destructive" : "secondary"}>
                {alertsCount.total} تنبيه
              </Badge>
            </div>
          </div>
        </CardHeader>

        <CardContent className="space-y-4">
          {/* Quick Stats */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
            <div className="text-center p-3 border rounded-lg">
              <div className="text-2xl font-bold text-red-600">{alertsCount.critical}</div>
              <div className="text-sm text-muted-foreground">حرج</div>
            </div>
            <div className="text-center p-3 border rounded-lg">
              <div className="text-2xl font-bold text-orange-600">{alertsCount.high}</div>
              <div className="text-sm text-muted-foreground">عالي</div>
            </div>
            <div className="text-center p-3 border rounded-lg">
              <div className="text-2xl font-bold text-blue-600">{alertsCount.actionRequired}</div>
              <div className="text-sm text-muted-foreground">يتطلب إجراء</div>
            </div>
            <div className="text-center p-3 border rounded-lg">
              <div className="text-2xl font-bold text-gray-600">{alertsCount.total}</div>
              <div className="text-sm text-muted-foreground">إجمالي</div>
            </div>
          </div>

          {/* Settings Panel */}
          {showSettings && (
            <Card className="border-dashed">
              <CardHeader>
                <CardTitle className="text-lg">إعدادات التنبيهات</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <Label>تفعيل التنبيهات</Label>
                  <Switch
                    checked={settings.enabled}
                    onCheckedChange={(checked) => 
                      setSettings(prev => ({ ...prev, enabled: checked }))
                    }
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="space-y-2">
                    <Label>حد الغياب المتتالي</Label>
                    <Input
                      type="number"
                      value={settings.absenceStreakThreshold}
                      onChange={(e) => 
                        setSettings(prev => ({ 
                          ...prev, 
                          absenceStreakThreshold: parseInt(e.target.value) || 3 
                        }))
                      }
                      min="1"
                      max="10"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label>حد الحضور المنخفض (%)</Label>
                    <Input
                      type="number"
                      value={settings.lowAttendanceThreshold}
                      onChange={(e) => 
                        setSettings(prev => ({ 
                          ...prev, 
                          lowAttendanceThreshold: parseInt(e.target.value) || 50 
                        }))
                      }
                      min="0"
                      max="100"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label>أيام عدم النشاط</Label>
                    <Input
                      type="number"
                      value={settings.inactivityDays}
                      onChange={(e) => 
                        setSettings(prev => ({ 
                          ...prev, 
                          inactivityDays: parseInt(e.target.value) || 30 
                        }))
                      }
                      min="7"
                      max="365"
                    />
                  </div>
                </div>

                <div className="flex items-center justify-between">
                  <Label>إرسال إشعارات تلقائية</Label>
                  <Switch
                    checked={settings.autoNotify}
                    onCheckedChange={(checked) => 
                      setSettings(prev => ({ ...prev, autoNotify: checked }))
                    }
                  />
                </div>
              </CardContent>
            </Card>
          )}
        </CardContent>
      </Card>

      {/* Alerts List */}
      {visibleAlerts.length > 0 ? (
        <div className="space-y-3">
          {Object.entries(alertsBySeverity).map(([severity, severityAlerts]) => 
            severityAlerts.length > 0 && (
              <Card key={severity} className={cn("border-l-4", getSeverityColor(severity))}>
                <CardHeader className="pb-3">
                  <CardTitle className="flex items-center gap-2 text-lg">
                    {getSeverityIcon(severity)}
                    تنبيهات {getSeverityText(severity)} ({severityAlerts.length})
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {severityAlerts.map((alert) => (
                      <div
                        key={alert.id}
                        className="flex items-center justify-between p-3 border rounded-lg hover:bg-muted/50 transition-colors"
                      >
                        <div className="flex items-center gap-3 flex-1">
                          {getAlertTypeIcon(alert.type)}
                          <div className="flex-1">
                            <div className="flex items-center gap-2 mb-1">
                              <span className="font-medium">{alert.userName}</span>
                              <Badge variant="outline" className="text-xs">
                                {getAlertTypeText(alert.type)}
                              </Badge>
                              {alert.actionRequired && (
                                <Badge variant="destructive" className="text-xs">
                                  يتطلب إجراء
                                </Badge>
                              )}
                            </div>
                            <p className="text-sm text-muted-foreground">{alert.message}</p>
                            <p className="text-xs text-muted-foreground">
                              {format(new Date(alert.createdAt), "dd MMMM yyyy، HH:mm", { locale: ar })}
                            </p>
                          </div>
                        </div>

                        <div className="flex items-center gap-2">
                          {/* Notification buttons */}
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleSendNotification(alert.id, "whatsapp")}
                            title="إرسال عبر واتساب"
                          >
                            <Send className="h-4 w-4" />
                          </Button>

                          {/* Action buttons */}
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleAlertAction(alert.id, "resolve")}
                            title="حل المشكلة"
                          >
                            <CheckCircle className="h-4 w-4 text-green-600" />
                          </Button>

                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleAlertAction(alert.id, "dismiss")}
                            title="إخفاء التنبيه"
                          >
                            <EyeOff className="h-4 w-4 text-gray-600" />
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )
          )}
        </div>
      ) : (
        <Card>
          <CardContent className="text-center py-8">
            <CheckCircle className="h-12 w-12 text-green-600 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-green-700 mb-2">
              لا توجد تنبيهات!
            </h3>
            <p className="text-muted-foreground">
              جميع الأعضاء في حالة جيدة ولا توجد مشاكل تتطلب انتباهك
            </p>
          </CardContent>
        </Card>
      )}

      {/* Dismissed Alerts */}
      {dismissedAlerts.size > 0 && (
        <Card className="border-dashed">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-sm">
              <EyeOff className="h-4 w-4" />
              التنبيهات المخفية ({dismissedAlerts.size})
            </CardTitle>
          </CardHeader>
          <CardContent>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setDismissedAlerts(new Set())}
              className="text-xs"
            >
              <Eye className="h-3 w-3 mr-1" />
              إظهار جميع التنبيهات المخفية
            </Button>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
