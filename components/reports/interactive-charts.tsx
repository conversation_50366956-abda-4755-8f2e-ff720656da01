"use client";

import React from "react";
import {
  AreaChart,
  Area,
  BarChart,
  Bar,
  LineChart,
  Line,
  PieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  <PERSON>lt<PERSON>,
  Legend,
  ResponsiveContainer,
} from "recharts";
import { <PERSON>, <PERSON>Content, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  TrendingUp,
  TrendingDown,
  Calendar,
  Users,
  BarChart3,
  <PERSON><PERSON><PERSON> as PieChartIcon,
  Activity,
} from "lucide-react";
import { format } from "date-fns";
import { ar } from "date-fns/locale";
import { cn } from "@/lib/utils";
import { useRTL } from "@/contexts/rtl-context";
import type { AttendanceTrendData, UserAttendanceAnalytics, CollegeComparisonData } from "@/lib/reports-analytics";

interface AttendanceTrendChartProps {
  data: AttendanceTrendData[];
  className?: string;
}

export function AttendanceTrendChart({ data, className }: AttendanceTrendChartProps) {
  const { isRTL } = useRTL();

  const formatDate = (dateStr: string) => {
    try {
      return format(new Date(dateStr), "dd/MM", { locale: ar });
    } catch {
      return dateStr;
    }
  };

  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      return (
        <div className="bg-background border rounded-lg p-3 shadow-lg">
          <p className="font-medium">{format(new Date(label), "EEEE، dd MMMM yyyy", { locale: ar })}</p>
          <div className="space-y-1 mt-2">
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 bg-green-500 rounded-full"></div>
              <span className="text-sm">حاضر: {data.present}</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 bg-red-500 rounded-full"></div>
              <span className="text-sm">غائب: {data.absent}</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
              <span className="text-sm">معدل الحضور: {data.attendanceRate}%</span>
            </div>
          </div>
        </div>
      );
    }
    return null;
  };

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Activity className="h-5 w-5 text-blue-600" />
          اتجاه الحضور عبر الزمن
        </CardTitle>
      </CardHeader>
      <CardContent>
        <ResponsiveContainer width="100%" height={300}>
          <AreaChart data={data} margin={{ top: 10, right: 30, left: 0, bottom: 0 }}>
            <defs>
              <linearGradient id="attendanceGradient" x1="0" y1="0" x2="0" y2="1">
                <stop offset="5%" stopColor="#3b82f6" stopOpacity={0.8}/>
                <stop offset="95%" stopColor="#3b82f6" stopOpacity={0.1}/>
              </linearGradient>
            </defs>
            <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
            <XAxis 
              dataKey="date" 
              tickFormatter={formatDate}
              reversed={isRTL}
            />
            <YAxis />
            <Tooltip content={<CustomTooltip />} />
            <Area
              type="monotone"
              dataKey="attendanceRate"
              stroke="#3b82f6"
              fillOpacity={1}
              fill="url(#attendanceGradient)"
            />
          </AreaChart>
        </ResponsiveContainer>
      </CardContent>
    </Card>
  );
}

interface AttendanceComparisonChartProps {
  data: UserAttendanceAnalytics[];
  title?: string;
  className?: string;
}

export function AttendanceComparisonChart({ 
  data, 
  title = "مقارنة معدلات الحضور", 
  className 
}: AttendanceComparisonChartProps) {
  const { isRTL } = useRTL();
  
  // Take top 10 users for better visualization
  const chartData = data.slice(0, 10).map(ua => ({
    name: ua.user.name.length > 15 ? ua.user.name.substring(0, 15) + "..." : ua.user.name,
    fullName: ua.user.name,
    attendanceRate: ua.attendanceRate,
    totalSessions: ua.totalSessions,
    presentSessions: ua.presentSessions,
    riskLevel: ua.riskLevel,
  }));

  const getBarColor = (riskLevel: string) => {
    switch (riskLevel) {
      case "low": return "#10b981";
      case "medium": return "#f59e0b";
      case "high": return "#ef4444";
      case "critical": return "#dc2626";
      default: return "#6b7280";
    }
  };

  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      return (
        <div className="bg-background border rounded-lg p-3 shadow-lg">
          <p className="font-medium">{data.fullName}</p>
          <div className="space-y-1 mt-2">
            <p className="text-sm">معدل الحضور: {data.attendanceRate}%</p>
            <p className="text-sm">الجلسات الحاضرة: {data.presentSessions}</p>
            <p className="text-sm">إجمالي الجلسات: {data.totalSessions}</p>
            <Badge 
              variant={data.riskLevel === "low" ? "default" : "destructive"}
              className="text-xs"
            >
              {data.riskLevel === "low" ? "منخفض المخاطر" : 
               data.riskLevel === "medium" ? "متوسط المخاطر" :
               data.riskLevel === "high" ? "عالي المخاطر" : "حرج"}
            </Badge>
          </div>
        </div>
      );
    }
    return null;
  };

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <BarChart3 className="h-5 w-5 text-green-600" />
          {title}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <ResponsiveContainer width="100%" height={300}>
          <BarChart data={chartData} margin={{ top: 10, right: 30, left: 0, bottom: 0 }}>
            <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
            <XAxis 
              dataKey="name" 
              angle={-45}
              textAnchor="end"
              height={80}
              reversed={isRTL}
            />
            <YAxis />
            <Tooltip content={<CustomTooltip />} />
            <Bar 
              dataKey="attendanceRate" 
              fill={(entry: any) => getBarColor(entry.riskLevel)}
              radius={[4, 4, 0, 0]}
            />
          </BarChart>
        </ResponsiveContainer>
      </CardContent>
    </Card>
  );
}

interface CollegeComparisonChartProps {
  data: CollegeComparisonData[];
  className?: string;
}

export function CollegeComparisonChart({ data, className }: CollegeComparisonChartProps) {
  const COLORS = ['#3b82f6', '#10b981', '#f59e0b', '#ef4444'];

  const pieData = data.map((college, index) => ({
    name: college.displayName,
    value: college.attendanceRate,
    totalUsers: college.totalUsers,
    topPerformers: college.topPerformers,
    needsAttention: college.needsAttention,
    color: COLORS[index % COLORS.length],
  }));

  const CustomTooltip = ({ active, payload }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      return (
        <div className="bg-background border rounded-lg p-3 shadow-lg">
          <p className="font-medium">{data.name}</p>
          <div className="space-y-1 mt-2">
            <p className="text-sm">معدل الحضور: {data.value}%</p>
            <p className="text-sm">إجمالي الأعضاء: {data.totalUsers}</p>
            <p className="text-sm">المتميزون: {data.topPerformers}</p>
            <p className="text-sm">يحتاج متابعة: {data.needsAttention}</p>
          </div>
        </div>
      );
    }
    return null;
  };

  const renderCustomLabel = ({ cx, cy, midAngle, innerRadius, outerRadius, percent }: any) => {
    const RADIAN = Math.PI / 180;
    const radius = innerRadius + (outerRadius - innerRadius) * 0.5;
    const x = cx + radius * Math.cos(-midAngle * RADIAN);
    const y = cy + radius * Math.sin(-midAngle * RADIAN);

    return (
      <text 
        x={x} 
        y={y} 
        fill="white" 
        textAnchor={x > cx ? 'start' : 'end'} 
        dominantBaseline="central"
        fontSize={12}
        fontWeight="bold"
      >
        {`${(percent * 100).toFixed(0)}%`}
      </text>
    );
  };

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <PieChartIcon className="h-5 w-5 text-purple-600" />
          مقارنة الكليات
        </CardTitle>
      </CardHeader>
      <CardContent>
        <ResponsiveContainer width="100%" height={300}>
          <PieChart>
            <Pie
              data={pieData}
              cx="50%"
              cy="50%"
              labelLine={false}
              label={renderCustomLabel}
              outerRadius={80}
              fill="#8884d8"
              dataKey="value"
            >
              {pieData.map((entry, index) => (
                <Cell key={`cell-${index}`} fill={entry.color} />
              ))}
            </Pie>
            <Tooltip content={<CustomTooltip />} />
            <Legend />
          </PieChart>
        </ResponsiveContainer>
        
        {/* Legend with additional info */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4">
          {data.map((college, index) => (
            <div key={college.category} className="flex items-center gap-3 p-3 border rounded-lg">
              <div 
                className="w-4 h-4 rounded-full"
                style={{ backgroundColor: COLORS[index % COLORS.length] }}
              />
              <div className="flex-1">
                <p className="font-medium text-sm">{college.displayName}</p>
                <p className="text-xs text-muted-foreground">
                  {college.totalUsers} عضو • {college.attendanceRate}%
                </p>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}

interface WeeklyTrendChartProps {
  data: UserAttendanceAnalytics[];
  className?: string;
}

export function WeeklyTrendChart({ data, className }: WeeklyTrendChartProps) {
  const { isRTL } = useRTL();

  // Group by trend and calculate counts
  const trendData = [
    {
      trend: "تحسن",
      count: data.filter(ua => ua.weeklyTrend.trend === "improving").length,
      color: "#10b981",
    },
    {
      trend: "مستقر",
      count: data.filter(ua => ua.weeklyTrend.trend === "stable").length,
      color: "#6b7280",
    },
    {
      trend: "تراجع",
      count: data.filter(ua => ua.weeklyTrend.trend === "declining").length,
      color: "#ef4444",
    },
  ];

  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      return (
        <div className="bg-background border rounded-lg p-3 shadow-lg">
          <p className="font-medium">{data.trend}</p>
          <p className="text-sm">عدد الأعضاء: {data.count}</p>
        </div>
      );
    }
    return null;
  };

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <TrendingUp className="h-5 w-5 text-blue-600" />
          الاتجاهات الأسبوعية
        </CardTitle>
      </CardHeader>
      <CardContent>
        <ResponsiveContainer width="100%" height={250}>
          <BarChart data={trendData} margin={{ top: 10, right: 30, left: 0, bottom: 0 }}>
            <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
            <XAxis dataKey="trend" reversed={isRTL} />
            <YAxis />
            <Tooltip content={<CustomTooltip />} />
            <Bar dataKey="count" fill="#3b82f6" radius={[4, 4, 0, 0]} />
          </BarChart>
        </ResponsiveContainer>
        
        {/* Summary cards */}
        <div className="grid grid-cols-3 gap-3 mt-4">
          {trendData.map((item) => (
            <div key={item.trend} className="text-center p-3 border rounded-lg">
              <div 
                className="text-2xl font-bold"
                style={{ color: item.color }}
              >
                {item.count}
              </div>
              <div className="text-sm text-muted-foreground">{item.trend}</div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}

interface RiskLevelDistributionProps {
  data: UserAttendanceAnalytics[];
  className?: string;
}

export function RiskLevelDistribution({ data, className }: RiskLevelDistributionProps) {
  const riskData = [
    {
      level: "منخفض",
      count: data.filter(ua => ua.riskLevel === "low").length,
      color: "#10b981",
      percentage: 0,
    },
    {
      level: "متوسط",
      count: data.filter(ua => ua.riskLevel === "medium").length,
      color: "#f59e0b",
      percentage: 0,
    },
    {
      level: "عالي",
      count: data.filter(ua => ua.riskLevel === "high").length,
      color: "#ef4444",
      percentage: 0,
    },
    {
      level: "حرج",
      count: data.filter(ua => ua.riskLevel === "critical").length,
      color: "#dc2626",
      percentage: 0,
    },
  ];

  // Calculate percentages
  const total = data.length;
  riskData.forEach(item => {
    item.percentage = total > 0 ? Math.round((item.count / total) * 100) : 0;
  });

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Users className="h-5 w-5 text-orange-600" />
          توزيع مستويات المخاطر
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {riskData.map((item) => (
            <div key={item.level} className="space-y-2">
              <div className="flex justify-between items-center">
                <span className="text-sm font-medium">{item.level}</span>
                <span className="text-sm text-muted-foreground">
                  {item.count} ({item.percentage}%)
                </span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div
                  className="h-2 rounded-full transition-all duration-500"
                  style={{ 
                    width: `${item.percentage}%`,
                    backgroundColor: item.color 
                  }}
                />
              </div>
            </div>
          ))}
        </div>
        
        {/* Summary */}
        <div className="mt-6 p-4 bg-muted rounded-lg">
          <div className="grid grid-cols-2 gap-4 text-center">
            <div>
              <div className="text-2xl font-bold text-green-600">
                {riskData[0].count + riskData[1].count}
              </div>
              <div className="text-sm text-muted-foreground">حالة جيدة</div>
            </div>
            <div>
              <div className="text-2xl font-bold text-red-600">
                {riskData[2].count + riskData[3].count}
              </div>
              <div className="text-sm text-muted-foreground">يحتاج متابعة</div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
