"use client";

import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  UserPlus,
  Save,
  QrCode,
  Download,
  Share,
  CheckCircle,
  User,
  Phone,
  Calendar,
  MapPin,
  GraduationCap,
  BookOpen,
  Building2,
  Sparkles,
} from "lucide-react";
import { useAppStore } from "@/stores/app-store";
import { useRTL } from "@/contexts/rtl-context";
import { format } from "date-fns";
import { FACULTY_OF_SCIENCE, FACULTY_OF_EDUCATION } from "@/lib/mock-data";

const formSchema = z
  .object({
    name: z.string().min(2, "الاسم يجب أن يكون أكثر من حرفين"),
    phone: z.string().min(11, "رقم الهاتف يجب أن يكون 11 رقم على الأقل"),
    gender: z.enum(["male", "female"], { required_error: "يرجى اختيار النوع" }),
    year: z.enum(["1", "2", "3", "4"], {
      required_error: "يرجى اختيار السنة الدراسية",
    }),
    college_category: z.enum(["science", "education", "other"], {
      required_error: "يرجى اختيار فئة الكلية",
    }),

    custom_college: z.string().optional(),
    department: z.string().min(2, "اسم القسم مطلوب"),
    birthdate: z.string().min(1, "تاريخ الميلاد مطلوب"),
    address: z.string().min(5, "العنوان يجب أن يكون أكثر تفصيلاً"),
    facebook_url: z.string().optional(),
    first_attendance_date: z.string().min(1, "تاريخ أول حضور مطلوب"),
  })
  .refine(
    (data) => {
      // If "other" is selected, custom_college must be provided
      if (data.college_category === "other" && !data.custom_college?.trim()) {
        return false;
      }
      return true;
    },
    {
      message: "يرجى إدخال اسم الكلية",
      path: ["custom_college"],
    }
  );

type FormData = z.infer<typeof formSchema>;

export default function AddUserPage() {
  const { addUser } = useAppStore();
  const { direction } = useRTL();
  const [showSuccess, setShowSuccess] = useState(false);
  const [generatedQR, setGeneratedQR] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [autoCloseTimer, setAutoCloseTimer] = useState<NodeJS.Timeout | null>(
    null
  );

  const form = useForm<FormData>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: "",
      phone: "",
      gender: undefined,
      year: undefined,
      college_category: undefined,
      custom_college: "",
      department: "",
      birthdate: "",
      address: "",
      facebook_url: "",
      first_attendance_date: format(new Date(), "yyyy-MM-dd"),
    },
  });

  // Watch college category to show/hide custom college input
  const collegeCategory = form.watch("college_category");

  // Handle form validation errors
  const onError = () => {
    // Handle validation errors silently in production
  };

  // Helper function to reset form and success state
  const resetFormAndSuccess = () => {
    if (autoCloseTimer) {
      clearTimeout(autoCloseTimer);
      setAutoCloseTimer(null);
    }
    setShowSuccess(false);
    setGeneratedQR(null);
    form.reset();
  };

  // Cleanup timer on component unmount
  useEffect(() => {
    return () => {
      if (autoCloseTimer) {
        clearTimeout(autoCloseTimer);
      }
    };
  }, [autoCloseTimer]);

  const onSubmit = async (data: FormData) => {
    if (isSubmitting) {
      return; // Prevent double submission
    }

    try {
      setIsSubmitting(true);

      // Determine the final college name based on category selection
      let finalCollege = "";
      if (data.college_category === "science") {
        finalCollege = FACULTY_OF_SCIENCE;
      } else if (data.college_category === "education") {
        finalCollege = FACULTY_OF_EDUCATION;
      } else if (data.college_category === "other") {
        finalCollege = data.custom_college || "";
      }

      // Validate that we have a college name
      if (!finalCollege.trim()) {
        form.setError("custom_college", {
          type: "manual",
          message: "يرجى إدخال اسم الكلية",
        });
        setIsSubmitting(false);
        return;
      }

      const userData = {
        name: data.name,
        phone: data.phone,
        gender: data.gender,
        year: Number.parseInt(data.year) as 1 | 2 | 3 | 4,
        college: finalCollege,
        department: data.department,
        birthdate: data.birthdate,
        address: data.address,
        facebook_url: data.facebook_url,
        first_attendance_date: data.first_attendance_date,
      };

      // Add user to store
      addUser(userData);

      // Set success state
      setGeneratedQR(`QR_${Date.now()}`);
      setShowSuccess(true);
      setIsSubmitting(false);

      // Auto-close after 10 seconds for better user experience
      const timer = setTimeout(() => {
        resetFormAndSuccess();
      }, 10000);
      setAutoCloseTimer(timer);
    } catch (error) {
      setIsSubmitting(false);
    }
  };

  if (showSuccess) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-green-50 via-white to-emerald-50 flex items-center justify-center">
        <div
          className="container mx-auto px-4 sm:px-6 lg:px-8 py-6 page-transition font-cairo"
          dir={direction}
        >
          <div className="max-w-2xl mx-auto">
            <Card className="animate-scale-in border-green-200 bg-white shadow-xl">
              <CardHeader className="text-center">
                <div className="flex justify-center mb-4">
                  <div className="w-16 h-16 bg-green-500 rounded-full flex items-center justify-center">
                    <CheckCircle className="h-8 w-8 text-white" />
                  </div>
                </div>
                <CardTitle className="text-2xl text-green-800">
                  تم إضافة العضو بنجاح!
                </CardTitle>
                <CardDescription className="text-green-600 text-lg">
                  تم إنشاء حساب العضو وتوليد QR Code الخاص به
                </CardDescription>
                <CardDescription className="text-green-500 text-sm mt-2">
                  سيتم إغلاق هذه الشاشة تلقائياً خلال 10 ثوانٍ
                </CardDescription>
              </CardHeader>
              <CardContent className="text-center space-y-6">
                <div className="w-48 h-48 bg-white rounded-lg mx-auto flex items-center justify-center border-2 border-green-200">
                  <QrCode className="h-24 w-24 text-green-500" />
                </div>
                <div>
                  <h3 className="font-medium text-green-800 mb-2">
                    QR Code جاهز للاستخدام
                  </h3>
                  <p className="text-sm text-green-600">
                    يمكن للعضو استخدام هذا الرمز لتسجيل الحضور
                  </p>
                </div>
                <div className="flex flex-col sm:flex-row gap-3 justify-center">
                  <Button
                    variant="outline"
                    className="border-green-300 text-green-700 bg-transparent hover:bg-green-50"
                  >
                    <Download className="h-4 w-4 mr-2" />
                    تحميل QR
                  </Button>
                  <Button
                    variant="outline"
                    className="border-green-300 text-green-700 bg-transparent hover:bg-green-50"
                  >
                    <Share className="h-4 w-4 mr-2" />
                    مشاركة
                  </Button>
                  <Button
                    className="bg-green-600 hover:bg-green-700 text-white"
                    onClick={resetFormAndSuccess}
                  >
                    <UserPlus className="h-4 w-4 mr-2" />
                    إضافة عضو آخر
                  </Button>
                  <Button
                    variant="secondary"
                    className="bg-gray-200 hover:bg-gray-300 text-gray-700"
                    onClick={resetFormAndSuccess}
                  >
                    إغلاق
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-green-50">
      <div
        className="container mx-auto px-4 sm:px-6 lg:px-8 py-6 lg:py-12 page-transition font-cairo"
        dir={direction}
      >
        {/* Enhanced Header */}
        <div className="animate-fade-in mb-8 lg:mb-12">
          <div className="text-center">
            <div className="inline-flex items-center justify-center p-3 sm:p-4 bg-gradient-to-r from-green-500 to-emerald-500 rounded-full mb-4 lg:mb-6 shadow-lg">
              <UserPlus className="h-6 w-6 sm:h-8 sm:w-8 text-white" />
            </div>
            <h1 className="text-3xl sm:text-4xl lg:text-5xl font-bold text-gray-900 mb-3 lg:mb-4">
              إضافة عضو جديد
            </h1>
            <p className="text-gray-600 text-lg sm:text-xl max-w-2xl mx-auto px-4">
              إضافة عضو جديد لنظام حضور الشباب مع إنشاء ملف شخصي متكامل
            </p>
            <div className="flex items-center justify-center gap-2 mt-4 lg:mt-6">
              <Sparkles className="h-4 w-4 sm:h-5 sm:w-5 text-yellow-500" />
              <span className="text-sm sm:text-base text-gray-500">
                سيتم إنشاء QR Code تلقائياً للعضو الجديد
              </span>
            </div>
          </div>
        </div>

        <div className="max-w-5xl mx-auto">
          <Card className="animate-scale-in shadow-xl border-0 bg-white/90 backdrop-blur-sm overflow-hidden">
            <CardHeader className="bg-gradient-to-r from-green-500 to-emerald-500 text-white p-6 lg:p-8">
              <CardTitle className="flex items-center gap-3 text-xl lg:text-2xl">
                <User className="h-6 w-6 lg:h-7 lg:w-7" />
                بيانات العضو الجديد
              </CardTitle>
              <CardDescription className="text-green-100 text-base lg:text-lg mt-2">
                يرجى ملء جميع البيانات المطلوبة بدقة
              </CardDescription>
            </CardHeader>
            <CardContent className="p-4 sm:p-6 lg:p-8">
              <Form {...form}>
                <form
                  onSubmit={form.handleSubmit(onSubmit, onError)}
                  className="space-y-6 lg:space-y-8"
                >
                  {/* Personal Information Section */}
                  <div className="bg-gradient-to-r from-blue-50 to-indigo-50 p-4 sm:p-6 lg:p-8 rounded-xl border border-blue-100">
                    <div className="flex items-center gap-3 mb-4 lg:mb-6">
                      <div className="p-2 lg:p-3 bg-blue-500 rounded-lg">
                        <User className="h-4 w-4 lg:h-5 lg:w-5 text-white" />
                      </div>
                      <h3 className="text-lg lg:text-xl font-bold text-gray-900">
                        البيانات الشخصية
                      </h3>
                    </div>

                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 lg:gap-6">
                      <FormField
                        control={form.control}
                        name="name"
                        render={({ field }) => (
                          <FormItem className="space-y-2">
                            <FormLabel className="flex items-center gap-2 text-gray-700 font-medium text-sm lg:text-base">
                              <User className="h-4 w-4 text-blue-600" />
                              الاسم الكامل
                            </FormLabel>
                            <FormControl>
                              <Input
                                placeholder="أدخل الاسم الكامل"
                                {...field}
                                className="h-10 sm:h-12 border-2 border-gray-200 focus:border-blue-500 transition-colors text-sm lg:text-base"
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="phone"
                        render={({ field }) => (
                          <FormItem className="space-y-2">
                            <FormLabel className="flex items-center gap-2 text-gray-700 font-medium text-sm lg:text-base">
                              <Phone className="h-4 w-4 text-blue-600" />
                              رقم الهاتف
                            </FormLabel>
                            <FormControl>
                              <Input
                                placeholder="01xxxxxxxxx"
                                {...field}
                                className="h-10 sm:h-12 border-2 border-gray-200 focus:border-blue-500 transition-colors text-sm lg:text-base"
                                dir="ltr"
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                  </div>

                  {/* Academic Information Section */}
                  <div className="bg-gradient-to-r from-green-50 to-emerald-50 p-4 sm:p-6 lg:p-8 rounded-xl border border-green-100">
                    <div className="flex items-center gap-3 mb-4 lg:mb-6">
                      <div className="p-2 lg:p-3 bg-green-500 rounded-lg">
                        <GraduationCap className="h-4 w-4 lg:h-5 lg:w-5 text-white" />
                      </div>
                      <h3 className="text-lg lg:text-xl font-bold text-gray-900">
                        البيانات الأكاديمية
                      </h3>
                    </div>

                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 lg:gap-6">
                      <FormField
                        control={form.control}
                        name="gender"
                        render={({ field }) => (
                          <FormItem className="space-y-2">
                            <FormLabel className="flex items-center gap-2 text-gray-700 font-medium text-sm lg:text-base">
                              <User className="h-4 w-4 text-green-600" />
                              النوع
                            </FormLabel>
                            <Select
                              onValueChange={field.onChange}
                              defaultValue={field.value}
                            >
                              <FormControl>
                                <SelectTrigger className="h-10 sm:h-12 border-2 border-gray-200 focus:border-green-500 text-sm lg:text-base">
                                  <SelectValue placeholder="اختر النوع" />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                <SelectItem value="male">ذكر</SelectItem>
                                <SelectItem value="female">أنثى</SelectItem>
                              </SelectContent>
                            </Select>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="year"
                        render={({ field }) => (
                          <FormItem className="space-y-2">
                            <FormLabel className="flex items-center gap-2 text-gray-700 font-medium text-sm lg:text-base">
                              <Calendar className="h-4 w-4 text-green-600" />
                              السنة الدراسية
                            </FormLabel>
                            <Select
                              onValueChange={field.onChange}
                              defaultValue={field.value}
                            >
                              <FormControl>
                                <SelectTrigger className="h-10 sm:h-12 border-2 border-gray-200 focus:border-green-500 text-sm lg:text-base">
                                  <SelectValue placeholder="اختر السنة الدراسية" />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                <SelectItem value="1">السنة الأولى</SelectItem>
                                <SelectItem value="2">السنة الثانية</SelectItem>
                                <SelectItem value="3">السنة الثالثة</SelectItem>
                                <SelectItem value="4">السنة الرابعة</SelectItem>
                              </SelectContent>
                            </Select>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    {/* College Category Selection */}
                    <div className="col-span-full">
                      <FormField
                        control={form.control}
                        name="college_category"
                        render={({ field }) => (
                          <FormItem className="space-y-2">
                            <FormLabel className="flex items-center gap-2 text-gray-700 font-medium text-sm lg:text-base">
                              <GraduationCap className="h-4 w-4 text-green-600" />
                              فئة الكلية
                            </FormLabel>
                            <Select
                              onValueChange={field.onChange}
                              defaultValue={field.value}
                            >
                              <FormControl>
                                <SelectTrigger className="h-10 sm:h-12 border-2 border-gray-200 focus:border-green-500 text-sm lg:text-base">
                                  <SelectValue placeholder="اختر فئة الكلية" />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                <SelectItem value="science">
                                  <div className="flex items-center gap-2">
                                    <GraduationCap className="h-4 w-4 text-blue-600" />
                                    <span>كلية العلوم</span>
                                  </div>
                                </SelectItem>
                                <SelectItem value="education">
                                  <div className="flex items-center gap-2">
                                    <BookOpen className="h-4 w-4 text-green-600" />
                                    <span>كلية التربية</span>
                                  </div>
                                </SelectItem>
                                <SelectItem value="other">
                                  <div className="flex items-center gap-2">
                                    <Building2 className="h-4 w-4 text-purple-600" />
                                    <span>أخرى</span>
                                  </div>
                                </SelectItem>
                              </SelectContent>
                            </Select>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    {/* Custom College Input - Only show when "other" is selected */}
                    {collegeCategory === "other" && (
                      <div className="col-span-full">
                        <FormField
                          control={form.control}
                          name="custom_college"
                          render={({ field }) => (
                            <FormItem className="space-y-2">
                              <FormLabel className="flex items-center gap-2 text-gray-700 font-medium text-sm lg:text-base">
                                <Building2 className="h-4 w-4 text-purple-600" />
                                اسم الكلية
                              </FormLabel>
                              <FormControl>
                                <Input
                                  placeholder="أدخل اسم الكلية"
                                  {...field}
                                  className="h-10 sm:h-12 border-2 border-gray-200 focus:border-purple-500 transition-colors text-sm lg:text-base"
                                />
                              </FormControl>
                              <FormDescription className="text-gray-600 text-xs lg:text-sm">
                                يرجى إدخال اسم الكلية بالكامل
                              </FormDescription>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>
                    )}

                    {/* Display selected college name for Science/Education */}
                    {(collegeCategory === "science" ||
                      collegeCategory === "education") && (
                      <div className="col-span-full">
                        <div className="p-3 sm:p-4 bg-gradient-to-r from-gray-50 to-gray-100 rounded-lg border-2 border-dashed border-gray-300">
                          <div className="flex items-start sm:items-center gap-3 text-gray-700">
                            {collegeCategory === "science" ? (
                              <GraduationCap className="h-5 w-5 sm:h-6 sm:w-6 text-blue-600 mt-1 sm:mt-0" />
                            ) : (
                              <BookOpen className="h-5 w-5 sm:h-6 sm:w-6 text-green-600 mt-1 sm:mt-0" />
                            )}
                            <div>
                              <span className="font-medium text-base lg:text-lg block">
                                الكلية المحددة:{" "}
                                {collegeCategory === "science"
                                  ? FACULTY_OF_SCIENCE
                                  : FACULTY_OF_EDUCATION}
                              </span>
                              <p className="text-xs lg:text-sm text-gray-500 mt-1">
                                سيتم حفظ هذه الكلية في ملف العضو
                              </p>
                            </div>
                          </div>
                        </div>
                      </div>
                    )}

                    <FormField
                      control={form.control}
                      name="department"
                      render={({ field }) => (
                        <FormItem className="space-y-2">
                          <FormLabel className="flex items-center gap-2 text-gray-700 font-medium text-sm lg:text-base">
                            <Building2 className="h-4 w-4 text-green-600" />
                            القسم
                          </FormLabel>
                          <FormControl>
                            <Input
                              placeholder="اسم القسم"
                              {...field}
                              className="h-10 sm:h-12 border-2 border-gray-200 focus:border-green-500 transition-colors text-sm lg:text-base"
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  {/* Personal Details Section */}
                  <div className="bg-gradient-to-r from-purple-50 to-pink-50 p-4 sm:p-6 lg:p-8 rounded-xl border border-purple-100">
                    <div className="flex items-center gap-3 mb-4 lg:mb-6">
                      <div className="p-2 lg:p-3 bg-purple-500 rounded-lg">
                        <Calendar className="h-4 w-4 lg:h-5 lg:w-5 text-white" />
                      </div>
                      <h3 className="text-lg lg:text-xl font-bold text-gray-900">
                        البيانات الشخصية الإضافية
                      </h3>
                    </div>

                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 lg:gap-6">
                      <FormField
                        control={form.control}
                        name="birthdate"
                        render={({ field }) => (
                          <FormItem className="space-y-2">
                            <FormLabel className="flex items-center gap-2 text-gray-700 font-medium text-sm lg:text-base">
                              <Calendar className="h-4 w-4 text-purple-600" />
                              تاريخ الميلاد
                            </FormLabel>
                            <FormControl>
                              <Input
                                type="date"
                                {...field}
                                className="h-10 sm:h-12 border-2 border-gray-200 focus:border-purple-500 transition-colors text-sm lg:text-base"
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="first_attendance_date"
                        render={({ field }) => (
                          <FormItem className="space-y-2">
                            <FormLabel className="flex items-center gap-2 text-gray-700 font-medium text-sm lg:text-base">
                              <Calendar className="h-4 w-4 text-purple-600" />
                              تاريخ أول حضور
                            </FormLabel>
                            <FormControl>
                              <Input
                                type="date"
                                {...field}
                                className="h-10 sm:h-12 border-2 border-gray-200 focus:border-purple-500 transition-colors text-sm lg:text-base"
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="address"
                        render={({ field }) => (
                          <FormItem className="col-span-full space-y-2">
                            <FormLabel className="flex items-center gap-2 text-gray-700 font-medium text-sm lg:text-base">
                              <MapPin className="h-4 w-4 text-purple-600" />
                              العنوان
                            </FormLabel>
                            <FormControl>
                              <Textarea
                                placeholder="العنوان التفصيلي"
                                className="resize-none border-2 border-gray-200 focus:border-purple-500 transition-colors text-sm lg:text-base min-h-[80px] sm:min-h-[100px]"
                                rows={3}
                                {...field}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="facebook_url"
                        render={({ field }) => (
                          <FormItem className="col-span-full space-y-2">
                            <FormLabel className="flex items-center gap-2 text-gray-700 font-medium text-sm lg:text-base">
                              <User className="h-4 w-4 text-purple-600" />
                              رابط الفيسبوك (اختياري)
                            </FormLabel>
                            <FormControl>
                              <Input
                                placeholder="https://facebook.com/username"
                                {...field}
                                className="h-10 sm:h-12 border-2 border-gray-200 focus:border-purple-500 transition-colors text-sm lg:text-base"
                                dir="ltr"
                              />
                            </FormControl>
                            <FormDescription className="text-gray-600 text-xs lg:text-sm">
                              يمكنك ترك هذا الحقل فارغاً إذا لم يكن لديك حساب
                              فيسبوك
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                  </div>

                  {/* Submit Button */}
                  <div className="flex justify-center pt-6 lg:pt-8">
                    <Button
                      type="submit"
                      className="w-full sm:w-auto bg-gradient-to-r from-green-500 to-emerald-500 hover:from-green-600 hover:to-emerald-600 text-white px-8 sm:px-12 py-3 sm:py-4 text-base lg:text-lg font-semibold rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
                      disabled={isSubmitting}
                    >
                      {isSubmitting ? (
                        <div className="flex items-center justify-center gap-2">
                          <div className="animate-spin rounded-full h-4 w-4 sm:h-5 sm:w-5 border-b-2 border-white"></div>
                          <span>جاري الحفظ...</span>
                        </div>
                      ) : (
                        <div className="flex items-center justify-center gap-2">
                          <Save className="h-4 w-4 sm:h-5 sm:w-5" />
                          <span>إضافة العضو</span>
                        </div>
                      )}
                    </Button>
                  </div>
                </form>
              </Form>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
