"use client";

import { useEffect ,useState, useMemo, useCallback } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  BarChart3,
  Download,
  Calendar,
  TrendingUp,
  FileText,
  PieChart,
  Activity,
  Award,
  AlertTriangle,
  GraduationCap,
  BookOpen,
  Building2,
  Users,
  Search,
  Filter,
  Bell,
} from "lucide-react";
import { useAppStore } from "@/stores/app-store";
import { useRTL } from "@/contexts/rtl-context";
import { cn } from "@/lib/utils";
import {
  format,
  subDays,
  startOfWeek,
  startOfMonth,
  endOfMonth,
} from "date-fns";
import { ar } from "date-fns/locale";
import {
  getAllCollegeAnalytics,
  getCollegeFilterOptions,
  filterUsersByCollegeCategory,
  CollegeCategory,
} from "@/lib/college-utils";

// Import new components
import { AdvancedSearchFilters } from "@/components/reports/advanced-search-filters";
import {
  AttendanceTrendChart,
  AttendanceComparisonChart,
  CollegeComparisonChart,
  WeeklyTrendChart,
  RiskLevelDistribution,
} from "@/components/reports/interactive-charts";
import { ExportControls } from "@/components/reports/export-controls";
import { BulkActions } from "@/components/reports/bulk-actions";
import { UserDetailModal } from "@/components/reports/user-detail-modal";
import { AlertsNotifications } from "@/components/reports/alerts-notifications";

// Import analytics utilities
import {
  generateDetailedReportData,
  calculateUserAttendanceAnalytics,
  generateAttendanceTrendData,
  type UserAttendanceAnalytics,
  type DetailedReportData,
  type AdvancedFilters,
  type SortOptions,
} from "@/lib/reports-analytics";
import {
  searchAndFilterUsers,
  defaultFilters,
  type SearchResult,
} from "@/lib/search-filter-utils";

export default function ReportsPage() {
  const { users, attendanceRecords } = useAppStore();
  const { isRTL, direction } = useRTL();

  // Enhanced state management
  const [activeTab, setActiveTab] = useState("overview");
  const [selectedPeriod, setSelectedPeriod] = useState("month");
  const [filters, setFilters] =
    useState<Partial<AdvancedFilters>>(defaultFilters);
  const [sortOptions, setSortOptions] = useState<SortOptions>({
    field: "attendanceRate",
    direction: "desc",
  });
  const [selectedUsers, setSelectedUsers] = useState<string[]>([]);
  const [selectedUserForDetail, setSelectedUserForDetail] =
    useState<UserAttendanceAnalytics | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  // Calculate comprehensive report data
  const reportData = useMemo(() => {
    const today = new Date();
    let dateRange: { from: Date; to: Date };

    switch (selectedPeriod) {
      case "week":
        dateRange = {
          from: startOfWeek(today, { weekStartsOn: 1 }),
          to: today,
        };
        break;
      case "month":
        dateRange = {
          from: startOfMonth(today),
          to: endOfMonth(today),
        };
        break;
      case "quarter":
        const quarterStart = new Date(
          today.getFullYear(),
          Math.floor(today.getMonth() / 3) * 3,
          1
        );
        dateRange = {
          from: quarterStart,
          to: today,
        };
        break;
      case "year":
        dateRange = {
          from: new Date(today.getFullYear(), 0, 1),
          to: today,
        };
        break;
      default:
        dateRange = {
          from: subDays(today, 90),
          to: today,
        };
    }

    return generateDetailedReportData(users, attendanceRecords, dateRange);
  }, [users, attendanceRecords, selectedPeriod]);

  // Calculate user analytics for search and filtering
  const userAnalytics = useMemo(() => {
    return users.map((user) =>
      calculateUserAttendanceAnalytics(user, attendanceRecords)
    );
  }, [users, attendanceRecords]);

  // Apply search and filtering
  const searchResults = useMemo(() => {
    return searchAndFilterUsers(users, userAnalytics, filters, sortOptions, {
      page: 1,
      pageSize: 50,
    });
  }, [users, userAnalytics, filters, sortOptions]);

  // Handle loading state separately
  useEffect(() => {
    setIsLoading(true);
    const timer = setTimeout(() => setIsLoading(false), 100);
    return () => clearTimeout(timer);
  }, [filters, sortOptions]);

  // Get college analytics
  const collegeAnalytics = useMemo(() => {
    return getAllCollegeAnalytics(users, attendanceRecords);
  }, [users, attendanceRecords]);

  // Event handlers
  const handleFiltersChange = useCallback(
    (
      newFilters:
        | Partial<AdvancedFilters>
        | ((prev: Partial<AdvancedFilters>) => Partial<AdvancedFilters>)
    ) => {
      if (typeof newFilters === "function") {
        setFilters(newFilters);
      } else {
        setFilters(newFilters);
      }
    },
    []
  );

  const handleSortChange = useCallback((newSort: SortOptions) => {
    setSortOptions(newSort);
  }, []);

  const handleUserSelection = useCallback((selectedIds: string[]) => {
    setSelectedUsers(selectedIds);
  }, []);

  const handleUserClick = useCallback(
    (userAnalytic: UserAttendanceAnalytics) => {
      setSelectedUserForDetail(userAnalytic);
    },
    []
  );

  const handleBulkAction = useCallback(
    async (userIds: string[], action: string, data?: any) => {
      // Implement bulk actions here
      console.log(
        "Bulk action:",
        action,
        "for users:",
        userIds,
        "with data:",
        data
      );
    },
    []
  );

  const handleAlertAction = useCallback(
    (alertId: string, action: "dismiss" | "resolve" | "snooze") => {
      // Implement alert actions here
      console.log("Alert action:", action, "for alert:", alertId);
    },
    []
  );

  const handleNotificationSend = useCallback(
    (alertId: string, method: "email" | "whatsapp") => {
      // Implement notification sending here
      console.log("Send notification:", method, "for alert:", alertId);
    },
    []
  );

  return (
    <div className="p-6 space-y-6 page-transition font-cairo" dir={direction}>
      {/* Header */}
      <div className="animate-fade-in">
        <div
          className={cn(
            "flex items-center justify-between",
            isRTL ? "flex-row-reverse" : "flex-row"
          )}
        >
          <div>
            <div
              className={cn(
                "flex items-center gap-3 mb-2",
                isRTL ? "flex-row-reverse" : "flex-row"
              )}
            >
              <div className="p-2 bg-gradient-to-r from-purple-500 to-pink-500 rounded-lg">
                <BarChart3 className="h-6 w-6 text-white" />
              </div>
              <h1 className="text-4xl font-bold text-heading gradient-text">
                التقارير والإحصائيات الشاملة
              </h1>
            </div>
            <p
              className={cn(
                "text-gray-600 text-lg text-body",
                isRTL ? "text-right" : "text-left"
              )}
            >
              تحليلات متقدمة وتقارير تفاعلية لحضور الأعضاء مع إمكانيات البحث
              والتصدير
            </p>
          </div>
          <div className="flex items-center gap-2">
            <Badge variant="outline" className="gap-1">
              <Users className="h-3 w-3" />
              {reportData.summary.totalUsers} عضو
            </Badge>
            <Badge variant="outline" className="gap-1">
              <Calendar className="h-3 w-3" />
              {reportData.summary.totalSessions} جلسة
            </Badge>
            <Badge variant="outline" className="gap-1">
              <Bell className="h-3 w-3" />
              {reportData.alerts.length} تنبيه
            </Badge>
          </div>
        </div>
      </div>

      {/* Period Selection */}
      <Card className="animate-scale-in">
        <CardHeader>
          <CardTitle>فترة التقرير</CardTitle>
          <CardDescription>اختر الفترة الزمنية للتحليل</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
            <Button
              variant={selectedPeriod === "week" ? "default" : "outline"}
              onClick={() => setSelectedPeriod("week")}
              className="justify-start"
            >
              <Calendar className="h-4 w-4 mr-2" />
              هذا الأسبوع
            </Button>
            <Button
              variant={selectedPeriod === "month" ? "default" : "outline"}
              onClick={() => setSelectedPeriod("month")}
              className="justify-start"
            >
              <Calendar className="h-4 w-4 mr-2" />
              هذا الشهر
            </Button>
            <Button
              variant={selectedPeriod === "quarter" ? "default" : "outline"}
              onClick={() => setSelectedPeriod("quarter")}
              className="justify-start"
            >
              <Calendar className="h-4 w-4 mr-2" />
              هذا الربع
            </Button>
            <Button
              variant={selectedPeriod === "year" ? "default" : "outline"}
              onClick={() => setSelectedPeriod("year")}
              className="justify-start"
            >
              <Calendar className="h-4 w-4 mr-2" />
              هذا العام
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Summary Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6 animate-slide-in-right">
        <Card className="hover-lift">
          <CardHeader
            className={cn(
              "flex items-center justify-between space-y-0 pb-2",
              isRTL ? "flex-row-reverse" : "flex-row"
            )}
          >
            <CardTitle className="text-sm font-medium">
              إجمالي الأعضاء
            </CardTitle>
            <Users className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {reportData.summary.totalUsers}
            </div>
            <p className="text-xs text-muted-foreground">
              {reportData.summary.activeUsers} نشط •{" "}
              {reportData.summary.inactiveUsers} غير نشط
            </p>
          </CardContent>
        </Card>

        <Card className="hover-lift">
          <CardHeader
            className={cn(
              "flex items-center justify-between space-y-0 pb-2",
              isRTL ? "flex-row-reverse" : "flex-row"
            )}
          >
            <CardTitle className="text-sm font-medium">
              معدل الحضور العام
            </CardTitle>
            <TrendingUp className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">
              {reportData.summary.overallAttendanceRate}%
            </div>
            <p className="text-xs text-muted-foreground">
              {reportData.timeComparison.improvement > 0 ? "+" : ""}
              {reportData.timeComparison.improvement}% من الفترة السابقة
            </p>
          </CardContent>
        </Card>

        <Card className="hover-lift">
          <CardHeader
            className={cn(
              "flex items-center justify-between space-y-0 pb-2",
              isRTL ? "flex-row-reverse" : "flex-row"
            )}
          >
            <CardTitle className="text-sm font-medium">
              إجمالي الجلسات
            </CardTitle>
            <Calendar className="h-4 w-4 text-purple-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-purple-600">
              {reportData.summary.totalSessions}
            </div>
            <p className="text-xs text-muted-foreground">في الفترة المحددة</p>
          </CardContent>
        </Card>

        <Card className="hover-lift">
          <CardHeader
            className={cn(
              "flex items-center justify-between space-y-0 pb-2",
              isRTL ? "flex-row-reverse" : "flex-row"
            )}
          >
            <CardTitle className="text-sm font-medium">التنبيهات</CardTitle>
            <AlertTriangle className="h-4 w-4 text-red-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">
              {
                reportData.alerts.filter(
                  (a) => a.severity === "critical" || a.severity === "high"
                ).length
              }
            </div>
            <p className="text-xs text-muted-foreground">
              {reportData.alerts.length} إجمالي التنبيهات
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Main Content Tabs */}
      <Tabs
        value={activeTab}
        onValueChange={setActiveTab}
        className="animate-fade-in"
      >
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="overview">نظرة عامة</TabsTrigger>
          <TabsTrigger value="analytics">التحليلات</TabsTrigger>
          <TabsTrigger value="users">الأعضاء</TabsTrigger>
          <TabsTrigger value="alerts">التنبيهات</TabsTrigger>
          <TabsTrigger value="export">التصدير</TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <AttendanceTrendChart
              data={reportData.trends}
              className="col-span-1 lg:col-span-2"
            />
            <CollegeComparisonChart data={reportData.collegeComparison} />
            <WeeklyTrendChart data={reportData.userAnalytics} />
          </div>
        </TabsContent>

        {/* Analytics Tab */}
        <TabsContent value="analytics" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <AttendanceComparisonChart
              data={reportData.userAnalytics.slice(0, 10)}
              title="أفضل 10 أعضاء في الحضور"
            />
            <RiskLevelDistribution data={reportData.userAnalytics} />
            <AttendanceComparisonChart
              data={reportData.userAnalytics
                .filter(
                  (ua) => ua.riskLevel === "high" || ua.riskLevel === "critical"
                )
                .slice(0, 10)}
              title="الأعضاء الذين يحتاجون متابعة"
            />
            <Card>
              <CardHeader>
                <CardTitle>مقارنة الفترات الزمنية</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <span>الفترة الحالية</span>
                    <Badge variant="default">
                      {
                        reportData.timeComparison.currentPeriod
                          .averageAttendance
                      }
                      %
                    </Badge>
                  </div>
                  <div className="flex justify-between items-center">
                    <span>الفترة السابقة</span>
                    <Badge variant="outline">
                      {
                        reportData.timeComparison.previousPeriod
                          .averageAttendance
                      }
                      %
                    </Badge>
                  </div>
                  <div className="flex justify-between items-center">
                    <span>التحسن</span>
                    <Badge
                      variant={
                        reportData.timeComparison.improvement >= 0
                          ? "default"
                          : "destructive"
                      }
                    >
                      {reportData.timeComparison.improvement >= 0 ? "+" : ""}
                      {reportData.timeComparison.improvement}%
                    </Badge>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Users Tab */}
        <TabsContent value="users" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <div className="lg:col-span-2 space-y-6">
              <AdvancedSearchFilters
                filters={filters}
                onFiltersChange={handleFiltersChange}
                sortOptions={sortOptions}
                onSortChange={handleSortChange}
                resultCount={searchResults.filteredCount}
                totalCount={searchResults.totalCount}
                isLoading={isLoading}
              />

              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Users className="h-5 w-5" />
                    قائمة الأعضاء ({searchResults.filteredCount})
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3 max-h-96 overflow-y-auto">
                    {searchResults.items.map((userAnalytic) => (
                      <div
                        key={userAnalytic.user.id}
                        className={cn(
                          "flex items-center justify-between p-3 border rounded-lg cursor-pointer hover:bg-muted/50 transition-colors",
                          selectedUsers.includes(userAnalytic.user.id) &&
                            "bg-blue-50 border-blue-200"
                        )}
                        onClick={() => handleUserClick(userAnalytic)}
                      >
                        <div className="flex items-center gap-3">
                          <input
                            type="checkbox"
                            checked={selectedUsers.includes(
                              userAnalytic.user.id
                            )}
                            onChange={(e) => {
                              e.stopPropagation();
                              if (e.target.checked) {
                                setSelectedUsers([
                                  ...selectedUsers,
                                  userAnalytic.user.id,
                                ]);
                              } else {
                                setSelectedUsers(
                                  selectedUsers.filter(
                                    (id) => id !== userAnalytic.user.id
                                  )
                                );
                              }
                            }}
                            className="rounded"
                          />
                          <div>
                            <div className="font-medium">
                              {userAnalytic.user.name}
                            </div>
                            <div className="text-sm text-muted-foreground">
                              {userAnalytic.user.college} • السنة{" "}
                              {userAnalytic.user.year}
                            </div>
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          <Badge
                            variant={
                              userAnalytic.attendanceRate >= 70
                                ? "default"
                                : "destructive"
                            }
                          >
                            {userAnalytic.attendanceRate}%
                          </Badge>
                          <Badge
                            variant="outline"
                            className={cn(
                              userAnalytic.riskLevel === "low" &&
                                "text-green-600",
                              userAnalytic.riskLevel === "medium" &&
                                "text-yellow-600",
                              userAnalytic.riskLevel === "high" &&
                                "text-orange-600",
                              userAnalytic.riskLevel === "critical" &&
                                "text-red-600"
                            )}
                          >
                            {userAnalytic.riskLevel === "low"
                              ? "منخفض"
                              : userAnalytic.riskLevel === "medium"
                              ? "متوسط"
                              : userAnalytic.riskLevel === "high"
                              ? "عالي"
                              : "حرج"}
                          </Badge>
                        </div>
                      </div>
                    ))}
                    {searchResults.items.length === 0 && (
                      <div className="text-center py-8 text-muted-foreground">
                        لا توجد نتائج تطابق البحث
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            </div>

            <div className="space-y-6">
              <BulkActions
                userAnalytics={reportData.userAnalytics}
                selectedUsers={selectedUsers}
                onSelectionChange={handleUserSelection}
                onUserUpdate={handleBulkAction}
              />
            </div>
          </div>
        </TabsContent>

        {/* Alerts Tab */}
        <TabsContent value="alerts" className="space-y-6">
          <AlertsNotifications
            alerts={reportData.alerts}
            userAnalytics={reportData.userAnalytics}
            onAlertAction={handleAlertAction}
            onNotificationSend={handleNotificationSend}
          />
        </TabsContent>

        {/* Export Tab */}
        <TabsContent value="export" className="space-y-6">
          <ExportControls reportData={reportData} />
        </TabsContent>
      </Tabs>

      {/* User Detail Modal */}
      <UserDetailModal
        userAnalytics={selectedUserForDetail}
        attendanceRecords={attendanceRecords}
        isOpen={!!selectedUserForDetail}
        onClose={() => setSelectedUserForDetail(null)}
      />
    </div>
  );
}
