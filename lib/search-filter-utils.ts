import { format, parseISO, isWithinInterval, startOfDay, endOfDay } from "date-fns";
import { ar } from "date-fns/locale";
import type { User, AttendanceRecord } from "@/stores/app-store";
import { getCollegeCategory, type CollegeCategory } from "@/lib/college-utils";
import type { UserAttendanceAnalytics, RiskLevel } from "./reports-analytics";

// Enhanced filter types for comprehensive search
export interface AdvancedFilters {
  // Basic filters
  searchQuery: string;
  year: string | "all";
  college: CollegeCategory | "all";
  gender: "male" | "female" | "all";
  
  // Date range filters
  dateRange: {
    from: string;
    to: string;
  } | null;
  
  // Attendance-specific filters
  attendanceRate: {
    min: number;
    max: number;
  } | null;
  
  riskLevel: RiskLevel | "all";
  attendanceStatus: "active" | "inactive" | "concerning" | "all";
  
  // Absence pattern filters
  consecutiveAbsences: {
    min: number;
    max: number;
  } | null;
  
  // Streak filters
  currentStreak: {
    min: number;
    max: number;
  } | null;
  
  // Trend filters
  weeklyTrend: "improving" | "declining" | "stable" | "all";
  
  // Last attendance filter
  lastAttendanceWithin: number | null; // days
}

export interface SearchResult<T> {
  items: T[];
  totalCount: number;
  filteredCount: number;
  hasMore: boolean;
  searchTime: number;
}

export interface SortOptions {
  field: string;
  direction: "asc" | "desc";
}

// Default filter values
export const defaultFilters: AdvancedFilters = {
  searchQuery: "",
  year: "all",
  college: "all",
  gender: "all",
  dateRange: null,
  attendanceRate: null,
  riskLevel: "all",
  attendanceStatus: "all",
  consecutiveAbsences: null,
  currentStreak: null,
  weeklyTrend: "all",
  lastAttendanceWithin: null,
};

/**
 * Advanced search and filter function for users with attendance analytics
 */
export function searchAndFilterUsers(
  users: User[],
  userAnalytics: UserAttendanceAnalytics[],
  filters: Partial<AdvancedFilters>,
  sortOptions?: SortOptions,
  pagination?: { page: number; pageSize: number }
): SearchResult<UserAttendanceAnalytics> {
  const startTime = performance.now();
  const effectiveFilters = { ...defaultFilters, ...filters };
  
  let filteredAnalytics = userAnalytics;

  // Apply search query filter
  if (effectiveFilters.searchQuery.trim()) {
    const query = effectiveFilters.searchQuery.toLowerCase().trim();
    filteredAnalytics = filteredAnalytics.filter(ua => 
      ua.user.name.toLowerCase().includes(query) ||
      ua.user.user_id.toLowerCase().includes(query) ||
      ua.user.phone.includes(query) ||
      ua.user.college.toLowerCase().includes(query) ||
      ua.user.department.toLowerCase().includes(query)
    );
  }

  // Apply year filter
  if (effectiveFilters.year !== "all") {
    const yearNum = parseInt(effectiveFilters.year);
    filteredAnalytics = filteredAnalytics.filter(ua => ua.user.year === yearNum);
  }

  // Apply college filter
  if (effectiveFilters.college !== "all") {
    filteredAnalytics = filteredAnalytics.filter(ua => 
      getCollegeCategory(ua.user.college) === effectiveFilters.college
    );
  }

  // Apply gender filter
  if (effectiveFilters.gender !== "all") {
    filteredAnalytics = filteredAnalytics.filter(ua => 
      ua.user.gender === effectiveFilters.gender
    );
  }

  // Apply attendance rate filter
  if (effectiveFilters.attendanceRate) {
    const { min, max } = effectiveFilters.attendanceRate;
    filteredAnalytics = filteredAnalytics.filter(ua => 
      ua.attendanceRate >= min && ua.attendanceRate <= max
    );
  }

  // Apply risk level filter
  if (effectiveFilters.riskLevel !== "all") {
    filteredAnalytics = filteredAnalytics.filter(ua => 
      ua.riskLevel === effectiveFilters.riskLevel
    );
  }

  // Apply attendance status filter
  if (effectiveFilters.attendanceStatus !== "all") {
    filteredAnalytics = filteredAnalytics.filter(ua => {
      switch (effectiveFilters.attendanceStatus) {
        case "active":
          return ua.totalSessions > 0 && ua.attendanceRate >= 70;
        case "inactive":
          return ua.totalSessions === 0;
        case "concerning":
          return ua.riskLevel === "high" || ua.riskLevel === "critical";
        default:
          return true;
      }
    });
  }

  // Apply consecutive absences filter
  if (effectiveFilters.consecutiveAbsences) {
    const { min, max } = effectiveFilters.consecutiveAbsences;
    filteredAnalytics = filteredAnalytics.filter(ua => 
      ua.absencePattern.consecutiveAbsences >= min && 
      ua.absencePattern.consecutiveAbsences <= max
    );
  }

  // Apply current streak filter
  if (effectiveFilters.currentStreak) {
    const { min, max } = effectiveFilters.currentStreak;
    filteredAnalytics = filteredAnalytics.filter(ua => 
      ua.currentStreak >= min && ua.currentStreak <= max
    );
  }

  // Apply weekly trend filter
  if (effectiveFilters.weeklyTrend !== "all") {
    filteredAnalytics = filteredAnalytics.filter(ua => 
      ua.weeklyTrend.trend === effectiveFilters.weeklyTrend
    );
  }

  // Apply last attendance filter
  if (effectiveFilters.lastAttendanceWithin !== null) {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - effectiveFilters.lastAttendanceWithin);
    
    filteredAnalytics = filteredAnalytics.filter(ua => {
      if (!ua.lastAttendance) return false;
      const lastAttendanceDate = parseISO(ua.lastAttendance);
      return lastAttendanceDate >= cutoffDate;
    });
  }

  // Apply date range filter (for user creation or first attendance)
  if (effectiveFilters.dateRange) {
    const { from, to } = effectiveFilters.dateRange;
    const fromDate = parseISO(from);
    const toDate = parseISO(to);
    
    filteredAnalytics = filteredAnalytics.filter(ua => {
      const userDate = parseISO(ua.user.first_attendance_date);
      return isWithinInterval(userDate, { start: fromDate, end: toDate });
    });
  }

  // Apply sorting
  if (sortOptions) {
    filteredAnalytics = sortUserAnalytics(filteredAnalytics, sortOptions);
  }

  const totalCount = userAnalytics.length;
  const filteredCount = filteredAnalytics.length;

  // Apply pagination
  let paginatedResults = filteredAnalytics;
  let hasMore = false;
  
  if (pagination) {
    const { page, pageSize } = pagination;
    const startIndex = (page - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    paginatedResults = filteredAnalytics.slice(startIndex, endIndex);
    hasMore = endIndex < filteredCount;
  }

  const searchTime = performance.now() - startTime;

  return {
    items: paginatedResults,
    totalCount,
    filteredCount,
    hasMore,
    searchTime,
  };
}

/**
 * Sort user analytics based on specified criteria
 */
function sortUserAnalytics(
  analytics: UserAttendanceAnalytics[],
  sortOptions: SortOptions
): UserAttendanceAnalytics[] {
  const { field, direction } = sortOptions;
  
  return [...analytics].sort((a, b) => {
    let aValue: any;
    let bValue: any;

    switch (field) {
      case "name":
        aValue = a.user.name;
        bValue = b.user.name;
        break;
      case "attendanceRate":
        aValue = a.attendanceRate;
        bValue = b.attendanceRate;
        break;
      case "totalSessions":
        aValue = a.totalSessions;
        bValue = b.totalSessions;
        break;
      case "currentStreak":
        aValue = a.currentStreak;
        bValue = b.currentStreak;
        break;
      case "longestStreak":
        aValue = a.longestStreak;
        bValue = b.longestStreak;
        break;
      case "lastAttendance":
        aValue = a.lastAttendance ? new Date(a.lastAttendance).getTime() : 0;
        bValue = b.lastAttendance ? new Date(b.lastAttendance).getTime() : 0;
        break;
      case "riskLevel":
        const riskOrder = { low: 1, medium: 2, high: 3, critical: 4 };
        aValue = riskOrder[a.riskLevel];
        bValue = riskOrder[b.riskLevel];
        break;
      case "year":
        aValue = a.user.year;
        bValue = b.user.year;
        break;
      case "college":
        aValue = a.user.college;
        bValue = b.user.college;
        break;
      case "consecutiveAbsences":
        aValue = a.absencePattern.consecutiveAbsences;
        bValue = b.absencePattern.consecutiveAbsences;
        break;
      case "weeklyChange":
        aValue = a.weeklyTrend.changePercentage;
        bValue = b.weeklyTrend.changePercentage;
        break;
      default:
        aValue = a.user.name;
        bValue = b.user.name;
    }

    // Handle string comparison
    if (typeof aValue === "string" && typeof bValue === "string") {
      const comparison = aValue.localeCompare(bValue, "ar");
      return direction === "asc" ? comparison : -comparison;
    }

    // Handle numeric comparison
    if (aValue < bValue) return direction === "asc" ? -1 : 1;
    if (aValue > bValue) return direction === "asc" ? 1 : -1;
    return 0;
  });
}

/**
 * Search attendance records with advanced filters
 */
export function searchAttendanceRecords(
  records: AttendanceRecord[],
  filters: {
    searchQuery?: string;
    dateRange?: { from: string; to: string };
    present?: boolean | null;
    markedBy?: string;
  }
): AttendanceRecord[] {
  let filteredRecords = records;

  // Apply search query
  if (filters.searchQuery?.trim()) {
    const query = filters.searchQuery.toLowerCase().trim();
    filteredRecords = filteredRecords.filter(record => 
      record.user_name.toLowerCase().includes(query) ||
      record.user_id.toLowerCase().includes(query) ||
      record.marked_by.toLowerCase().includes(query)
    );
  }

  // Apply date range filter
  if (filters.dateRange) {
    const { from, to } = filters.dateRange;
    const fromDate = startOfDay(parseISO(from));
    const toDate = endOfDay(parseISO(to));
    
    filteredRecords = filteredRecords.filter(record => {
      const recordDate = parseISO(record.date);
      return isWithinInterval(recordDate, { start: fromDate, end: toDate });
    });
  }

  // Apply presence filter
  if (filters.present !== null && filters.present !== undefined) {
    filteredRecords = filteredRecords.filter(record => 
      record.present === filters.present
    );
  }

  // Apply marked by filter
  if (filters.markedBy?.trim()) {
    filteredRecords = filteredRecords.filter(record => 
      record.marked_by.toLowerCase().includes(filters.markedBy!.toLowerCase())
    );
  }

  return filteredRecords;
}

/**
 * Get filter suggestions based on current data
 */
export function getFilterSuggestions(
  users: User[],
  userAnalytics: UserAttendanceAnalytics[]
): {
  colleges: string[];
  departments: string[];
  riskLevels: { value: RiskLevel; count: number }[];
  attendanceRanges: { min: number; max: number; count: number }[];
} {
  const colleges = [...new Set(users.map(u => u.college))].sort();
  const departments = [...new Set(users.map(u => u.department))].sort();
  
  const riskLevelCounts = userAnalytics.reduce((acc, ua) => {
    acc[ua.riskLevel] = (acc[ua.riskLevel] || 0) + 1;
    return acc;
  }, {} as Record<RiskLevel, number>);

  const riskLevels = Object.entries(riskLevelCounts).map(([level, count]) => ({
    value: level as RiskLevel,
    count,
  }));

  // Create attendance rate ranges
  const attendanceRanges = [
    { min: 0, max: 25, count: 0 },
    { min: 26, max: 50, count: 0 },
    { min: 51, max: 75, count: 0 },
    { min: 76, max: 100, count: 0 },
  ];

  userAnalytics.forEach(ua => {
    const range = attendanceRanges.find(r => 
      ua.attendanceRate >= r.min && ua.attendanceRate <= r.max
    );
    if (range) range.count++;
  });

  return {
    colleges,
    departments,
    riskLevels,
    attendanceRanges: attendanceRanges.filter(r => r.count > 0),
  };
}

/**
 * Debounced search hook utility
 */
export function useDebounce<T>(value: T, delay: number): T {
  const [debouncedValue, setDebouncedValue] = React.useState<T>(value);

  React.useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);

  return debouncedValue;
}

// Import React for the hook
import React from "react";
