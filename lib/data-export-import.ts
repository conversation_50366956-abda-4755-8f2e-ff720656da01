import { format } from "date-fns";
import { ar } from "date-fns/locale";
import type { User, AttendanceRecord } from "@/stores/app-store";

// Export formats
export type ExportFormat = "csv" | "excel" | "pdf";

// Filter types for export
export interface ExportFilters {
  year?: string;
  college?: string;
  gender?: string;
  status?: string;
  dateRange?: {
    from: string;
    to: string;
  };
}

// Import result type
export interface ImportResult {
  success: boolean;
  data?: User[];
  errors?: string[];
  warnings?: string[];
  totalRows?: number;
  validRows?: number;
}

/**
 * Export users data to CSV format
 */
export function exportToCSV(users: User[], filename?: string): void {
  const headers = [
    "معرف المستخدم",
    "الاسم",
    "الهاتف",
    "النوع",
    "السنة",
    "الكلية",
    "القسم",
    "تاريخ الميلاد",
    "العنوان",
    "الفيسبوك",
    "تاريخ أول حضور",
    "تاريخ الإنشاء",
  ];

  const csvContent = [
    headers.join(","),
    ...users.map((user) =>
      [
        `"${user.user_id}"`,
        `"${user.name}"`,
        `"${user.phone}"`,
        user.gender === "male" ? "ذكر" : "أنثى",
        user.year.toString(),
        `"${user.college}"`,
        `"${user.department}"`,
        user.birthdate,
        `"${user.address}"`,
        `"${user.facebook_url || ""}"`,
        user.first_attendance_date,
        format(new Date(user.created_at), "yyyy-MM-dd"),
      ].join(",")
    ),
  ].join("\n");

  // Add BOM for proper Arabic text encoding
  const BOM = "\uFEFF";
  const blob = new Blob([BOM + csvContent], {
    type: "text/csv;charset=utf-8;",
  });

  const link = document.createElement("a");
  link.href = URL.createObjectURL(blob);
  link.download = filename || `users_${format(new Date(), "yyyy-MM-dd")}.csv`;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
}

/**
 * Export users data to Excel format (using CSV with Excel-compatible formatting)
 */
export function exportToExcel(users: User[], filename?: string): void {
  // For now, we'll use CSV format that Excel can open properly
  // In a real implementation, you'd use a library like xlsx
  const headers = [
    "معرف المستخدم",
    "الاسم",
    "الهاتف",
    "النوع",
    "السنة",
    "الكلية",
    "القسم",
    "تاريخ الميلاد",
    "العنوان",
    "الفيسبوك",
    "تاريخ أول حضور",
    "تاريخ الإنشاء",
  ];

  const csvContent = [
    headers.join("\t"), // Use tabs for Excel compatibility
    ...users.map((user) =>
      [
        user.user_id,
        user.name,
        user.phone,
        user.gender === "male" ? "ذكر" : "أنثى",
        user.year.toString(),
        user.college,
        user.department,
        user.birthdate,
        user.address,
        user.facebook_url || "",
        user.first_attendance_date,
        format(new Date(user.created_at), "yyyy-MM-dd"),
      ].join("\t")
    ),
  ].join("\n");

  const BOM = "\uFEFF";
  const blob = new Blob([BOM + csvContent], {
    type: "application/vnd.ms-excel;charset=utf-8;",
  });

  const link = document.createElement("a");
  link.href = URL.createObjectURL(blob);
  link.download = filename || `users_${format(new Date(), "yyyy-MM-dd")}.xls`;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
}

/**
 * Export users data to PDF format
 */
export function exportToPDF(users: User[], filename?: string): void {
  // Create a simple HTML table for PDF generation
  const htmlContent = `
    <!DOCTYPE html>
    <html dir="rtl" lang="ar">
    <head>
      <meta charset="UTF-8">
      <title>قائمة المستخدمين</title>
      <style>
        body { 
          font-family: Arial, sans-serif; 
          direction: rtl; 
          text-align: right;
          margin: 20px;
        }
        table { 
          width: 100%; 
          border-collapse: collapse; 
          margin-top: 20px;
        }
        th, td { 
          border: 1px solid #ddd; 
          padding: 8px; 
          text-align: right;
        }
        th { 
          background-color: #f2f2f2; 
          font-weight: bold;
        }
        .header {
          text-align: center;
          margin-bottom: 20px;
        }
        .stats {
          margin-bottom: 20px;
          padding: 10px;
          background-color: #f9f9f9;
          border-radius: 5px;
        }
      </style>
    </head>
    <body>
      <div class="header">
        <h1>قائمة المستخدمين</h1>
        <p>تاريخ التصدير: ${format(new Date(), "dd MMMM yyyy", {
          locale: ar,
        })}</p>
      </div>
      
      <div class="stats">
        <p><strong>إجمالي المستخدمين:</strong> ${users.length}</p>
        <p><strong>الذكور:</strong> ${
          users.filter((u) => u.gender === "male").length
        }</p>
        <p><strong>الإناث:</strong> ${
          users.filter((u) => u.gender === "female").length
        }</p>
      </div>

      <table>
        <thead>
          <tr>
            <th>معرف المستخدم</th>
            <th>الاسم</th>
            <th>الهاتف</th>
            <th>النوع</th>
            <th>السنة</th>
            <th>الكلية</th>
            <th>القسم</th>
            <th>تاريخ الميلاد</th>
            <th>العنوان</th>
          </tr>
        </thead>
        <tbody>
          ${users
            .map(
              (user) => `
            <tr>
              <td>${user.user_id}</td>
              <td>${user.name}</td>
              <td>${user.phone}</td>
              <td>${user.gender === "male" ? "ذكر" : "أنثى"}</td>
              <td>السنة ${user.year}</td>
              <td>${user.college}</td>
              <td>${user.department}</td>
              <td>${format(new Date(user.birthdate), "dd/MM/yyyy")}</td>
              <td>${user.address}</td>
            </tr>
          `
            )
            .join("")}
        </tbody>
      </table>
    </body>
    </html>
  `;

  // Create a new window and print
  const printWindow = window.open("", "_blank");
  if (printWindow) {
    printWindow.document.write(htmlContent);
    printWindow.document.close();
    printWindow.focus();

    // Wait for content to load then print
    setTimeout(() => {
      printWindow.print();
      printWindow.close();
    }, 1000);
  }
}

/**
 * Filter users based on export filters
 */
export function filterUsersForExport(
  users: User[],
  filters?: ExportFilters
): User[] {
  if (!filters) return users;

  let filtered = users;

  // Year filter
  if (filters.year && filters.year !== "all") {
    filtered = filtered.filter((user) => user.year.toString() === filters.year);
  }

  // College filter
  if (filters.college && filters.college !== "all") {
    filtered = filtered.filter((user) =>
      user.college.toLowerCase().includes(filters.college!.toLowerCase())
    );
  }

  // Gender filter
  if (filters.gender && filters.gender !== "all") {
    filtered = filtered.filter((user) => user.gender === filters.gender);
  }

  // Status filter (placeholder for future implementation)
  if (filters.status && filters.status !== "all") {
    // For now, all users are considered active
    // This can be extended when user status is implemented
  }

  // Date range filter (based on creation date)
  if (filters.dateRange?.from) {
    filtered = filtered.filter(
      (user) => new Date(user.created_at) >= new Date(filters.dateRange!.from)
    );
  }

  if (filters.dateRange?.to) {
    filtered = filtered.filter(
      (user) => new Date(user.created_at) <= new Date(filters.dateRange!.to)
    );
  }

  return filtered;
}

/**
 * Export comprehensive attendance report to CSV
 */
export function exportAttendanceReportToCSV(
  reportData: any,
  filename?: string
): void {
  const headers = [
    "معرف المستخدم",
    "الاسم",
    "الكلية",
    "السنة",
    "إجمالي الجلسات",
    "جلسات الحضور",
    "جلسات الغياب",
    "معدل الحضور %",
    "الحضور المتتالي الحالي",
    "أطول فترة حضور متتالية",
    "آخر حضور",
    "مستوى المخاطر",
    "الغياب المتتالي",
    "الاتجاه الأسبوعي",
    "التغيير الأسبوعي %",
  ];

  const csvContent = [
    headers.join(","),
    ...reportData.userAnalytics.map((ua: any) =>
      [
        `"${ua.user.user_id}"`,
        `"${ua.user.name}"`,
        `"${ua.user.college}"`,
        ua.user.year.toString(),
        ua.totalSessions.toString(),
        ua.presentSessions.toString(),
        ua.absentSessions.toString(),
        ua.attendanceRate.toString(),
        ua.currentStreak.toString(),
        ua.longestStreak.toString(),
        `"${ua.lastAttendance || "لا يوجد"}"`,
        `"${getRiskLevelText(ua.riskLevel)}"`,
        ua.absencePattern.consecutiveAbsences.toString(),
        `"${getTrendText(ua.weeklyTrend.trend)}"`,
        ua.weeklyTrend.changePercentage.toString(),
      ].join(",")
    ),
  ].join("\n");

  const BOM = "\uFEFF";
  const blob = new Blob([BOM + csvContent], {
    type: "text/csv;charset=utf-8;",
  });

  const link = document.createElement("a");
  link.href = URL.createObjectURL(blob);
  link.download =
    filename || `attendance_report_${format(new Date(), "yyyy-MM-dd")}.csv`;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
}

/**
 * Export comprehensive attendance report to Excel
 */
export function exportAttendanceReportToExcel(
  reportData: any,
  filename?: string
): void {
  const headers = [
    "معرف المستخدم",
    "الاسم",
    "الكلية",
    "السنة",
    "إجمالي الجلسات",
    "جلسات الحضور",
    "جلسات الغياب",
    "معدل الحضور %",
    "الحضور المتتالي الحالي",
    "أطول فترة حضور متتالية",
    "آخر حضور",
    "مستوى المخاطر",
    "الغياب المتتالي",
    "الاتجاه الأسبوعي",
    "التغيير الأسبوعي %",
  ];

  const csvContent = [
    headers.join("\t"),
    ...reportData.userAnalytics.map((ua: any) =>
      [
        ua.user.user_id,
        ua.user.name,
        ua.user.college,
        ua.user.year.toString(),
        ua.totalSessions.toString(),
        ua.presentSessions.toString(),
        ua.absentSessions.toString(),
        ua.attendanceRate.toString(),
        ua.currentStreak.toString(),
        ua.longestStreak.toString(),
        ua.lastAttendance || "لا يوجد",
        getRiskLevelText(ua.riskLevel),
        ua.absencePattern.consecutiveAbsences.toString(),
        getTrendText(ua.weeklyTrend.trend),
        ua.weeklyTrend.changePercentage.toString(),
      ].join("\t")
    ),
  ].join("\n");

  const BOM = "\uFEFF";
  const blob = new Blob([BOM + csvContent], {
    type: "application/vnd.ms-excel;charset=utf-8;",
  });

  const link = document.createElement("a");
  link.href = URL.createObjectURL(blob);
  link.download =
    filename || `attendance_report_${format(new Date(), "yyyy-MM-dd")}.xls`;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
}

/**
 * Export comprehensive attendance report to PDF
 */
export function exportAttendanceReportToPDF(
  reportData: any,
  filename?: string
): void {
  // Create a comprehensive HTML report
  const htmlContent = generateHTMLReport(reportData);

  // Create a new window for printing
  const printWindow = window.open("", "_blank");
  if (printWindow) {
    printWindow.document.write(htmlContent);
    printWindow.document.close();

    // Wait for content to load then print
    printWindow.onload = () => {
      printWindow.print();
      printWindow.close();
    };
  }
}

/**
 * Generate HTML report for PDF export
 */
function generateHTMLReport(reportData: any): string {
  const currentDate = format(new Date(), "dd MMMM yyyy", { locale: ar });

  return `
    <!DOCTYPE html>
    <html dir="rtl" lang="ar">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>تقرير الحضور الشامل</title>
      <style>
        body {
          font-family: 'Arial', sans-serif;
          direction: rtl;
          text-align: right;
          margin: 20px;
          line-height: 1.6;
        }
        .header {
          text-align: center;
          border-bottom: 2px solid #3b82f6;
          padding-bottom: 20px;
          margin-bottom: 30px;
        }
        .summary {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
          gap: 20px;
          margin-bottom: 30px;
        }
        .summary-card {
          border: 1px solid #e5e7eb;
          border-radius: 8px;
          padding: 15px;
          text-align: center;
        }
        .summary-card h3 {
          margin: 0 0 10px 0;
          color: #374151;
        }
        .summary-card .value {
          font-size: 24px;
          font-weight: bold;
          color: #3b82f6;
        }
        table {
          width: 100%;
          border-collapse: collapse;
          margin-bottom: 30px;
        }
        th, td {
          border: 1px solid #e5e7eb;
          padding: 8px;
          text-align: center;
        }
        th {
          background-color: #f3f4f6;
          font-weight: bold;
        }
        .risk-low { color: #10b981; }
        .risk-medium { color: #f59e0b; }
        .risk-high { color: #ef4444; }
        .risk-critical { color: #dc2626; font-weight: bold; }
        .trend-improving { color: #10b981; }
        .trend-declining { color: #ef4444; }
        .trend-stable { color: #6b7280; }
        @media print {
          body { margin: 0; }
          .no-print { display: none; }
        }
      </style>
    </head>
    <body>
      <div class="header">
        <h1>تقرير الحضور الشامل</h1>
        <p>تاريخ التقرير: ${currentDate}</p>
      </div>

      <div class="summary">
        <div class="summary-card">
          <h3>إجمالي الأعضاء</h3>
          <div class="value">${reportData.summary.totalUsers}</div>
        </div>
        <div class="summary-card">
          <h3>إجمالي الجلسات</h3>
          <div class="value">${reportData.summary.totalSessions}</div>
        </div>
        <div class="summary-card">
          <h3>معدل الحضور العام</h3>
          <div class="value">${reportData.summary.overallAttendanceRate}%</div>
        </div>
        <div class="summary-card">
          <h3>الأعضاء النشطون</h3>
          <div class="value">${reportData.summary.activeUsers}</div>
        </div>
      </div>

      <h2>تفاصيل حضور الأعضاء</h2>
      <table>
        <thead>
          <tr>
            <th>الاسم</th>
            <th>الكلية</th>
            <th>السنة</th>
            <th>معدل الحضور</th>
            <th>الحضور المتتالي</th>
            <th>مستوى المخاطر</th>
            <th>الاتجاه</th>
          </tr>
        </thead>
        <tbody>
          ${reportData.userAnalytics
            .map(
              (ua: any) => `
            <tr>
              <td>${ua.user.name}</td>
              <td>${ua.user.college}</td>
              <td>${ua.user.year}</td>
              <td>${ua.attendanceRate}%</td>
              <td>${ua.currentStreak}</td>
              <td class="risk-${ua.riskLevel}">${getRiskLevelText(
                ua.riskLevel
              )}</td>
              <td class="trend-${ua.weeklyTrend.trend}">${getTrendText(
                ua.weeklyTrend.trend
              )}</td>
            </tr>
          `
            )
            .join("")}
        </tbody>
      </table>

      ${
        reportData.alerts.length > 0
          ? `
        <h2>التنبيهات والتحذيرات</h2>
        <table>
          <thead>
            <tr>
              <th>النوع</th>
              <th>الاسم</th>
              <th>الرسالة</th>
              <th>الأولوية</th>
            </tr>
          </thead>
          <tbody>
            ${reportData.alerts
              .slice(0, 20)
              .map(
                (alert: any) => `
              <tr>
                <td>${getAlertTypeText(alert.type)}</td>
                <td>${alert.userName}</td>
                <td>${alert.message}</td>
                <td class="risk-${alert.severity}">${getSeverityText(
                  alert.severity
                )}</td>
              </tr>
            `
              )
              .join("")}
          </tbody>
        </table>
      `
          : ""
      }
    </body>
    </html>
  `;
}

// Helper functions for text conversion
function getRiskLevelText(level: string): string {
  switch (level) {
    case "low":
      return "منخفض";
    case "medium":
      return "متوسط";
    case "high":
      return "عالي";
    case "critical":
      return "حرج";
    default:
      return "غير محدد";
  }
}

function getTrendText(trend: string): string {
  switch (trend) {
    case "improving":
      return "تحسن";
    case "declining":
      return "تراجع";
    case "stable":
      return "مستقر";
    default:
      return "غير محدد";
  }
}

function getAlertTypeText(type: string): string {
  switch (type) {
    case "absence_streak":
      return "غياب متتالي";
    case "declining_trend":
      return "اتجاه متراجع";
    case "low_attendance":
      return "حضور منخفض";
    case "inactive_user":
      return "عضو غير نشط";
    default:
      return "غير محدد";
  }
}

function getSeverityText(severity: string): string {
  switch (severity) {
    case "low":
      return "منخفض";
    case "medium":
      return "متوسط";
    case "high":
      return "عالي";
    case "critical":
      return "حرج";
    default:
      return "غير محدد";
  }
}

/**
 * Main export function that handles all formats
 */
export function exportUsers(
  users: User[],
  format: ExportFormat,
  filters?: ExportFilters,
  filename?: string
): void {
  const filteredUsers = filterUsersForExport(users, filters);

  if (filteredUsers.length === 0) {
    alert("لا توجد بيانات للتصدير بناءً على الفلاتر المحددة");
    return;
  }

  switch (format) {
    case "csv":
      exportToCSV(filteredUsers, filename);
      break;
    case "excel":
      exportToExcel(filteredUsers, filename);
      break;
    case "pdf":
      exportToPDF(filteredUsers, filename);
      break;
    default:
      throw new Error(`Unsupported export format: ${format}`);
  }
}

/**
 * Export comprehensive reports in various formats
 */
export function exportComprehensiveReport(
  reportData: any,
  format: ExportFormat,
  filename?: string
): void {
  switch (format) {
    case "csv":
      exportAttendanceReportToCSV(reportData, filename);
      break;
    case "excel":
      exportAttendanceReportToExcel(reportData, filename);
      break;
    case "pdf":
      exportAttendanceReportToPDF(reportData, filename);
      break;
    default:
      throw new Error(`Unsupported export format: ${format}`);
  }
}

/**
 * Parse CSV content and validate user data
 */
export function parseCSVImport(csvContent: string): ImportResult {
  try {
    const lines = csvContent.split("\n").filter((line) => line.trim());

    if (lines.length < 2) {
      return {
        success: false,
        errors: ["الملف فارغ أو لا يحتوي على بيانات صالحة"],
      };
    }

    const headers = lines[0].split(",").map((h) => h.trim().replace(/"/g, ""));
    const dataLines = lines.slice(1);

    const users: User[] = [];
    const errors: string[] = [];
    const warnings: string[] = [];

    dataLines.forEach((line, index) => {
      try {
        const values = line.split(",").map((v) => v.trim().replace(/"/g, ""));

        if (values.length < 8) {
          errors.push(`الصف ${index + 2}: بيانات ناقصة`);
          return;
        }

        const user: User = {
          id: `imported_${Date.now()}_${index}`,
          user_id: values[0] || `USR${(index + 1).toString().padStart(3, "0")}`,
          name: values[1] || "",
          phone: values[2] || "",
          gender: values[3] === "أنثى" ? "female" : "male",
          year: parseInt(values[4]) as 1 | 2 | 3 | 4,
          college: values[5] || "",
          department: values[6] || "",
          birthdate: values[7] || "",
          address: values[8] || "",
          facebook_url: values[9] || "",
          first_attendance_date: values[10] || format(new Date(), "yyyy-MM-dd"),
          qr_code: `QR_${
            values[0] || `USR${(index + 1).toString().padStart(3, "0")}`
          }_${Date.now()}`,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        };

        // Validation
        if (!user.name) {
          errors.push(`الصف ${index + 2}: الاسم مطلوب`);
          return;
        }

        if (!user.phone || user.phone.length < 11) {
          errors.push(`الصف ${index + 2}: رقم الهاتف غير صحيح`);
          return;
        }

        if (!user.year || user.year < 1 || user.year > 4) {
          errors.push(`الصف ${index + 2}: السنة الدراسية غير صحيحة`);
          return;
        }

        users.push(user);
      } catch (error) {
        errors.push(`الصف ${index + 2}: خطأ في تحليل البيانات`);
      }
    });

    return {
      success: errors.length === 0,
      data: users,
      errors: errors.length > 0 ? errors : undefined,
      warnings: warnings.length > 0 ? warnings : undefined,
      totalRows: dataLines.length,
      validRows: users.length,
    };
  } catch (error) {
    return {
      success: false,
      errors: ["خطأ في قراءة الملف. تأكد من أن الملف بصيغة CSV صحيحة"],
    };
  }
}
