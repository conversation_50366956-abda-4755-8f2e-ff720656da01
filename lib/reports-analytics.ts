import {
  format,
  subDays,
  startOfWeek,
  startOfMonth,
  endOfMonth,
  differenceInDays,
  parseISO,
  isWithinInterval,
} from "date-fns";
import { ar } from "date-fns/locale";
import type { User, AttendanceRecord } from "@/stores/app-store";
import { getCollegeCategory, type CollegeCategory } from "@/lib/college-utils";

// Enhanced types for comprehensive analytics
export interface UserAttendanceAnalytics {
  user: User;
  totalSessions: number;
  presentSessions: number;
  absentSessions: number;
  attendanceRate: number;
  currentStreak: number;
  longestStreak: number;
  lastAttendance: string | null;
  absencePattern: AbsencePattern;
  riskLevel: RiskLevel;
  monthlyStats: MonthlyStats[];
  weeklyTrend: WeeklyTrend;
}

export interface AbsencePattern {
  consecutiveAbsences: number;
  frequentAbsenceDays: string[]; // Days of week with frequent absences
  seasonalTrends: SeasonalTrend[];
  totalAbsenceReasons: Record<string, number>;
}

export interface SeasonalTrend {
  period: string;
  attendanceRate: number;
  trend: "improving" | "declining" | "stable";
}

export interface MonthlyStats {
  month: string;
  year: number;
  totalSessions: number;
  presentSessions: number;
  attendanceRate: number;
}

export interface WeeklyTrend {
  currentWeek: number;
  previousWeek: number;
  trend: "improving" | "declining" | "stable";
  changePercentage: number;
}

export type RiskLevel = "low" | "medium" | "high" | "critical";

export interface AttendanceTrendData {
  date: string;
  present: number;
  absent: number;
  total: number;
  attendanceRate: number;
  dayOfWeek: string;
}

export interface ComparisonAnalytics {
  currentPeriod: PeriodStats;
  previousPeriod: PeriodStats;
  improvement: number;
  trend: "improving" | "declining" | "stable";
}

export interface PeriodStats {
  startDate: string;
  endDate: string;
  totalSessions: number;
  averageAttendance: number;
  topPerformers: UserAttendanceAnalytics[];
  concerningUsers: UserAttendanceAnalytics[];
}

export interface DetailedReportData {
  summary: {
    totalUsers: number;
    totalSessions: number;
    overallAttendanceRate: number;
    activeUsers: number;
    inactiveUsers: number;
  };
  trends: AttendanceTrendData[];
  userAnalytics: UserAttendanceAnalytics[];
  collegeComparison: CollegeComparisonData[];
  timeComparison: ComparisonAnalytics;
  alerts: AttendanceAlert[];
}

export interface CollegeComparisonData {
  category: CollegeCategory;
  displayName: string;
  totalUsers: number;
  attendanceRate: number;
  topPerformers: number;
  needsAttention: number;
  trend: "improving" | "declining" | "stable";
}

export interface AttendanceAlert {
  id: string;
  type:
    | "absence_streak"
    | "declining_trend"
    | "low_attendance"
    | "inactive_user";
  severity: "low" | "medium" | "high" | "critical";
  userId: string;
  userName: string;
  message: string;
  actionRequired: boolean;
  createdAt: string;
}

// Utility functions for analytics calculations

/**
 * Calculate comprehensive user attendance analytics
 */
export function calculateUserAttendanceAnalytics(
  user: User,
  attendanceRecords: AttendanceRecord[],
  dateRange?: { from: Date; to: Date }
): UserAttendanceAnalytics {
  const userRecords = attendanceRecords
    .filter((record) => record.user_id === user.id)
    .filter((record) => {
      if (!dateRange) return true;
      const recordDate = parseISO(record.date);
      return isWithinInterval(recordDate, dateRange);
    })
    .sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());

  const totalSessions = userRecords.length;
  const presentSessions = userRecords.filter((r) => r.present).length;
  const absentSessions = totalSessions - presentSessions;
  const attendanceRate =
    totalSessions > 0 ? Math.round((presentSessions / totalSessions) * 100) : 0;

  // Calculate streaks
  const { currentStreak, longestStreak } =
    calculateAttendanceStreaks(userRecords);

  // Find last attendance
  const lastAttendanceRecord = userRecords
    .filter((r) => r.present)
    .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())[0];
  const lastAttendance = lastAttendanceRecord
    ? lastAttendanceRecord.date
    : null;

  // Calculate absence patterns
  const absencePattern = calculateAbsencePattern(userRecords);

  // Determine risk level
  const riskLevel = determineRiskLevel(
    attendanceRate,
    absencePattern.consecutiveAbsences,
    totalSessions
  );

  // Calculate monthly stats
  const monthlyStats = calculateMonthlyStats(userRecords);

  // Calculate weekly trend
  const weeklyTrend = calculateWeeklyTrend(userRecords);

  return {
    user,
    totalSessions,
    presentSessions,
    absentSessions,
    attendanceRate,
    currentStreak,
    longestStreak,
    lastAttendance,
    absencePattern,
    riskLevel,
    monthlyStats,
    weeklyTrend,
  };
}

/**
 * Calculate attendance streaks (current and longest)
 */
function calculateAttendanceStreaks(records: AttendanceRecord[]): {
  currentStreak: number;
  longestStreak: number;
} {
  if (records.length === 0) return { currentStreak: 0, longestStreak: 0 };

  let currentStreak = 0;
  let longestStreak = 0;
  let tempStreak = 0;

  // Sort by date descending for current streak
  const sortedRecords = [...records].sort(
    (a, b) => new Date(b.date).getTime() - new Date(a.date).getTime()
  );

  // Calculate current streak (from most recent)
  for (const record of sortedRecords) {
    if (record.present) {
      currentStreak++;
    } else {
      break;
    }
  }

  // Calculate longest streak
  for (const record of records) {
    if (record.present) {
      tempStreak++;
      longestStreak = Math.max(longestStreak, tempStreak);
    } else {
      tempStreak = 0;
    }
  }

  return { currentStreak, longestStreak };
}

/**
 * Calculate absence patterns and trends
 */
function calculateAbsencePattern(records: AttendanceRecord[]): AbsencePattern {
  const absentRecords = records.filter((r) => !r.present);

  // Calculate consecutive absences (from most recent)
  const sortedRecords = [...records].sort(
    (a, b) => new Date(b.date).getTime() - new Date(a.date).getTime()
  );
  let consecutiveAbsences = 0;
  for (const record of sortedRecords) {
    if (!record.present) {
      consecutiveAbsences++;
    } else {
      break;
    }
  }

  // Find frequent absence days (days of week)
  const absenceDayCounts: Record<string, number> = {};
  absentRecords.forEach((record) => {
    const dayOfWeek = format(parseISO(record.date), "EEEE", { locale: ar });
    absenceDayCounts[dayOfWeek] = (absenceDayCounts[dayOfWeek] || 0) + 1;
  });

  const frequentAbsenceDays = Object.entries(absenceDayCounts)
    .filter(([_, count]) => count >= 2)
    .map(([day, _]) => day);

  // Calculate seasonal trends (quarterly)
  const seasonalTrends = calculateSeasonalTrends(records);

  return {
    consecutiveAbsences,
    frequentAbsenceDays,
    seasonalTrends,
    totalAbsenceReasons: {}, // Placeholder for future absence reason tracking
  };
}

/**
 * Calculate seasonal attendance trends
 */
function calculateSeasonalTrends(records: AttendanceRecord[]): SeasonalTrend[] {
  const quarters = ["Q1", "Q2", "Q3", "Q4"];
  const currentYear = new Date().getFullYear();

  return quarters.map((quarter) => {
    const quarterRecords = records.filter((record) => {
      const date = parseISO(record.date);
      const month = date.getMonth();
      const year = date.getFullYear();

      if (year !== currentYear) return false;

      switch (quarter) {
        case "Q1":
          return month >= 0 && month <= 2;
        case "Q2":
          return month >= 3 && month <= 5;
        case "Q3":
          return month >= 6 && month <= 8;
        case "Q4":
          return month >= 9 && month <= 11;
        default:
          return false;
      }
    });

    const presentCount = quarterRecords.filter((r) => r.present).length;
    const attendanceRate =
      quarterRecords.length > 0
        ? Math.round((presentCount / quarterRecords.length) * 100)
        : 0;

    return {
      period: quarter,
      attendanceRate,
      trend: "stable" as const, // Simplified for now
    };
  });
}

/**
 * Determine user risk level based on attendance patterns
 */
function determineRiskLevel(
  attendanceRate: number,
  consecutiveAbsences: number,
  totalSessions: number
): RiskLevel {
  if (totalSessions < 3) return "low"; // Not enough data

  if (attendanceRate >= 80 && consecutiveAbsences <= 1) return "low";
  if (attendanceRate >= 60 && consecutiveAbsences <= 2) return "medium";
  if (attendanceRate >= 40 && consecutiveAbsences <= 3) return "high";

  return "critical";
}

/**
 * Calculate monthly attendance statistics
 */
function calculateMonthlyStats(records: AttendanceRecord[]): MonthlyStats[] {
  const monthlyData: Record<
    string,
    { present: number; total: number; year: number; month: string }
  > = {};

  records.forEach((record) => {
    const date = parseISO(record.date);
    const monthKey = format(date, "yyyy-MM");
    const monthName = format(date, "MMMM", { locale: ar });
    const year = date.getFullYear();

    if (!monthlyData[monthKey]) {
      monthlyData[monthKey] = { present: 0, total: 0, year, month: monthName };
    }

    monthlyData[monthKey].total++;
    if (record.present) {
      monthlyData[monthKey].present++;
    }
  });

  return Object.entries(monthlyData).map(([key, data]) => ({
    month: data.month,
    year: data.year,
    totalSessions: data.total,
    presentSessions: data.present,
    attendanceRate: Math.round((data.present / data.total) * 100),
  }));
}

/**
 * Calculate weekly attendance trend
 */
function calculateWeeklyTrend(records: AttendanceRecord[]): WeeklyTrend {
  const now = new Date();
  const currentWeekStart = startOfWeek(now, { weekStartsOn: 1 });
  const previousWeekStart = startOfWeek(subDays(now, 7), { weekStartsOn: 1 });

  const currentWeekRecords = records.filter((record) => {
    const date = parseISO(record.date);
    return date >= currentWeekStart;
  });

  const previousWeekRecords = records.filter((record) => {
    const date = parseISO(record.date);
    return date >= previousWeekStart && date < currentWeekStart;
  });

  const currentWeekRate =
    currentWeekRecords.length > 0
      ? Math.round(
          (currentWeekRecords.filter((r) => r.present).length /
            currentWeekRecords.length) *
            100
        )
      : 0;

  const previousWeekRate =
    previousWeekRecords.length > 0
      ? Math.round(
          (previousWeekRecords.filter((r) => r.present).length /
            previousWeekRecords.length) *
            100
        )
      : 0;

  const changePercentage =
    previousWeekRate > 0 ? currentWeekRate - previousWeekRate : 0;

  let trend: "improving" | "declining" | "stable" = "stable";
  if (Math.abs(changePercentage) >= 10) {
    trend = changePercentage > 0 ? "improving" : "declining";
  }

  return {
    currentWeek: currentWeekRate,
    previousWeek: previousWeekRate,
    trend,
    changePercentage,
  };
}

/**
 * Generate attendance trend data for charts
 */
export function generateAttendanceTrendData(
  attendanceRecords: AttendanceRecord[],
  dateRange: { from: Date; to: Date }
): AttendanceTrendData[] {
  const filteredRecords = attendanceRecords.filter((record) => {
    const recordDate = parseISO(record.date);
    return isWithinInterval(recordDate, dateRange);
  });

  // Group by date
  const dailyData: Record<
    string,
    { present: number; absent: number; total: number }
  > = {};

  filteredRecords.forEach((record) => {
    const date = record.date;
    if (!dailyData[date]) {
      dailyData[date] = { present: 0, absent: 0, total: 0 };
    }

    dailyData[date].total++;
    if (record.present) {
      dailyData[date].present++;
    } else {
      dailyData[date].absent++;
    }
  });

  return Object.entries(dailyData)
    .map(([date, data]) => ({
      date,
      present: data.present,
      absent: data.absent,
      total: data.total,
      attendanceRate: Math.round((data.present / data.total) * 100),
      dayOfWeek: format(parseISO(date), "EEEE", { locale: ar }),
    }))
    .sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());
}

/**
 * Generate comprehensive report data
 */
export function generateDetailedReportData(
  users: User[],
  attendanceRecords: AttendanceRecord[],
  dateRange?: { from: Date; to: Date }
): DetailedReportData {
  const effectiveDateRange = dateRange || {
    from: subDays(new Date(), 90),
    to: new Date(),
  };

  // Calculate user analytics
  const userAnalytics = users.map((user) =>
    calculateUserAttendanceAnalytics(
      user,
      attendanceRecords,
      effectiveDateRange
    )
  );

  // Generate summary
  const totalUsers = users.length;
  const totalSessions = attendanceRecords.filter((record) => {
    const recordDate = parseISO(record.date);
    return isWithinInterval(recordDate, effectiveDateRange);
  }).length;

  const overallAttendanceRate =
    userAnalytics.length > 0
      ? Math.round(
          userAnalytics.reduce((sum, ua) => sum + ua.attendanceRate, 0) /
            userAnalytics.length
        )
      : 0;

  const activeUsers = userAnalytics.filter((ua) => ua.totalSessions > 0).length;
  const inactiveUsers = totalUsers - activeUsers;

  // Generate trends
  const trends = generateAttendanceTrendData(
    attendanceRecords,
    effectiveDateRange
  );

  // College comparison
  const collegeComparison = generateCollegeComparisonData(
    users,
    attendanceRecords,
    effectiveDateRange
  );

  // Time comparison (current vs previous period)
  const timeComparison = generateTimeComparisonData(
    attendanceRecords,
    effectiveDateRange
  );

  // Generate alerts
  const alerts = generateAttendanceAlerts(userAnalytics);

  return {
    summary: {
      totalUsers,
      totalSessions,
      overallAttendanceRate,
      activeUsers,
      inactiveUsers,
    },
    trends,
    userAnalytics,
    collegeComparison,
    timeComparison,
    alerts,
  };
}

/**
 * Generate college comparison data
 */
function generateCollegeComparisonData(
  users: User[],
  attendanceRecords: AttendanceRecord[],
  dateRange: { from: Date; to: Date }
): CollegeComparisonData[] {
  const categories: CollegeCategory[] = ["science", "education", "others"];

  return categories.map((category) => {
    const categoryUsers = users.filter(
      (user) => getCollegeCategory(user.college) === category
    );
    const categoryRecords = attendanceRecords.filter((record) => {
      const recordDate = parseISO(record.date);
      return (
        isWithinInterval(recordDate, dateRange) &&
        categoryUsers.some((user) => user.id === record.user_id)
      );
    });

    const presentCount = categoryRecords.filter((r) => r.present).length;
    const attendanceRate =
      categoryRecords.length > 0
        ? Math.round((presentCount / categoryRecords.length) * 100)
        : 0;

    const userAnalytics = categoryUsers.map((user) =>
      calculateUserAttendanceAnalytics(user, attendanceRecords, dateRange)
    );

    const topPerformers = userAnalytics.filter(
      (ua) => ua.attendanceRate >= 80
    ).length;
    const needsAttention = userAnalytics.filter(
      (ua) => ua.riskLevel === "high" || ua.riskLevel === "critical"
    ).length;

    return {
      category,
      displayName: getCategoryDisplayName(category),
      totalUsers: categoryUsers.length,
      attendanceRate,
      topPerformers,
      needsAttention,
      trend: "stable" as const, // Simplified for now
    };
  });
}

/**
 * Generate time comparison data
 */
function generateTimeComparisonData(
  attendanceRecords: AttendanceRecord[],
  currentRange: { from: Date; to: Date }
): ComparisonAnalytics {
  const periodLength = differenceInDays(currentRange.to, currentRange.from);
  const previousRange = {
    from: subDays(currentRange.from, periodLength),
    to: currentRange.from,
  };

  const currentPeriodRecords = attendanceRecords.filter((record) => {
    const recordDate = parseISO(record.date);
    return isWithinInterval(recordDate, currentRange);
  });

  const previousPeriodRecords = attendanceRecords.filter((record) => {
    const recordDate = parseISO(record.date);
    return isWithinInterval(recordDate, previousRange);
  });

  const currentStats = calculatePeriodStats(currentPeriodRecords, currentRange);
  const previousStats = calculatePeriodStats(
    previousPeriodRecords,
    previousRange
  );

  const improvement =
    currentStats.averageAttendance - previousStats.averageAttendance;
  let trend: "improving" | "declining" | "stable" = "stable";

  if (Math.abs(improvement) >= 5) {
    trend = improvement > 0 ? "improving" : "declining";
  }

  return {
    currentPeriod: currentStats,
    previousPeriod: previousStats,
    improvement,
    trend,
  };
}

/**
 * Calculate period statistics
 */
function calculatePeriodStats(
  records: AttendanceRecord[],
  range: { from: Date; to: Date }
): PeriodStats {
  const totalSessions = records.length;
  const presentCount = records.filter((r) => r.present).length;
  const averageAttendance =
    totalSessions > 0 ? Math.round((presentCount / totalSessions) * 100) : 0;

  return {
    startDate: format(range.from, "yyyy-MM-dd"),
    endDate: format(range.to, "yyyy-MM-dd"),
    totalSessions,
    averageAttendance,
    topPerformers: [], // Simplified for now
    concerningUsers: [], // Simplified for now
  };
}

/**
 * Generate attendance alerts
 */
function generateAttendanceAlerts(
  userAnalytics: UserAttendanceAnalytics[]
): AttendanceAlert[] {
  const alerts: AttendanceAlert[] = [];

  userAnalytics.forEach((ua) => {
    // Absence streak alert
    if (ua.absencePattern.consecutiveAbsences >= 3) {
      alerts.push({
        id: `absence_streak_${ua.user.id}`,
        type: "absence_streak",
        severity:
          ua.absencePattern.consecutiveAbsences >= 5 ? "critical" : "high",
        userId: ua.user.id,
        userName: ua.user.name,
        message: `غياب متتالي لمدة ${ua.absencePattern.consecutiveAbsences} جلسات`,
        actionRequired: true,
        createdAt: new Date().toISOString(),
      });
    }

    // Low attendance alert
    if (ua.attendanceRate < 50 && ua.totalSessions >= 5) {
      alerts.push({
        id: `low_attendance_${ua.user.id}`,
        type: "low_attendance",
        severity: ua.attendanceRate < 30 ? "critical" : "high",
        userId: ua.user.id,
        userName: ua.user.name,
        message: `معدل حضور منخفض: ${ua.attendanceRate}%`,
        actionRequired: true,
        createdAt: new Date().toISOString(),
      });
    }

    // Declining trend alert
    if (
      ua.weeklyTrend.trend === "declining" &&
      Math.abs(ua.weeklyTrend.changePercentage) >= 20
    ) {
      alerts.push({
        id: `declining_trend_${ua.user.id}`,
        type: "declining_trend",
        severity: "medium",
        userId: ua.user.id,
        userName: ua.user.name,
        message: `انخفاض في معدل الحضور بنسبة ${Math.abs(
          ua.weeklyTrend.changePercentage
        )}%`,
        actionRequired: false,
        createdAt: new Date().toISOString(),
      });
    }

    // Inactive user alert
    if (ua.totalSessions === 0) {
      alerts.push({
        id: `inactive_user_${ua.user.id}`,
        type: "inactive_user",
        severity: "low",
        userId: ua.user.id,
        userName: ua.user.name,
        message: "لم يحضر أي جلسة في الفترة المحددة",
        actionRequired: false,
        createdAt: new Date().toISOString(),
      });
    }
  });

  return alerts.sort((a, b) => {
    const severityOrder = { critical: 4, high: 3, medium: 2, low: 1 };
    return severityOrder[b.severity] - severityOrder[a.severity];
  });
}

// Helper function to get category display name
function getCategoryDisplayName(category: CollegeCategory): string {
  switch (category) {
    case "science":
      return "كلية العلوم";
    case "education":
      return "كلية التربية";
    case "others":
      return "الكليات الأخرى";
    default:
      return "غير محدد";
  }
}
